{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\JusSamy\\\\frontend\\\\src\\\\components\\\\ProductsTab.tsx\",\n  _s = $RefreshSig$();\n// @ts-nocheck\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, Button, Card, CardContent, Chip, TextField, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, Alert, Grid } from '@mui/material';\nimport { Add as AddIcon, Warning as WarningIcon, TrendingDown as TrendingDownIcon, Inventory as InventoryIcon } from '@mui/icons-material';\nimport { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';\nimport { productsAPI, categoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsTab = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [filters, setFilters] = useState({\n    category: '',\n    stockStatus: '',\n    isFinishedProduct: ''\n  });\n\n  // Form state for product creation/editing\n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    price: 0,\n    currentStock: 0,\n    minStockLevel: 0,\n    isFinishedProduct: false\n  });\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [filters]);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll(filters);\n      setProducts(response.data.products);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to fetch products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await categoriesAPI.getAll();\n      setCategories(response.data.categories);\n    } catch (err) {\n      console.error('Failed to fetch categories:', err);\n    }\n  };\n  const handleCreateProduct = async () => {\n    try {\n      await productsAPI.create(formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchProducts();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to create product');\n    }\n  };\n  const handleUpdateProduct = async () => {\n    if (!selectedProduct) return;\n    try {\n      await productsAPI.update(selectedProduct._id, formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchProducts();\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to update product');\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      category: '',\n      price: 0,\n      currentStock: 0,\n      minStockLevel: 0,\n      isFinishedProduct: false\n    });\n    setSelectedProduct(null);\n  };\n  const openCreateDialog = () => {\n    resetForm();\n    setOpenDialog(true);\n  };\n  const openEditDialog = product => {\n    var _product$category;\n    setSelectedProduct(product);\n    setFormData({\n      name: product.name,\n      category: ((_product$category = product.category) === null || _product$category === void 0 ? void 0 : _product$category._id) || '',\n      price: product.price,\n      currentStock: product.currentStock,\n      minStockLevel: product.minStockLevel,\n      isFinishedProduct: product.isFinishedProduct\n    });\n    setOpenDialog(true);\n  };\n  const getStockStatusColor = status => {\n    switch (status) {\n      case 'low':\n        return 'warning';\n      case 'out':\n        return 'error';\n      case 'high':\n        return 'info';\n      default:\n        return 'success';\n    }\n  };\n  const columns = [{\n    field: 'name',\n    headerName: 'Product Name',\n    width: 200\n  }, {\n    field: 'category',\n    headerName: 'Category',\n    width: 150,\n    valueGetter: params => {\n      var _params$row$category;\n      return ((_params$row$category = params.row.category) === null || _params$row$category === void 0 ? void 0 : _params$row$category.name) || 'No Category';\n    }\n  }, {\n    field: 'price',\n    headerName: 'Price',\n    width: 100,\n    valueFormatter: params => `$${params.value.toFixed(2)}`\n  }, {\n    field: 'currentStock',\n    headerName: 'Current Stock',\n    width: 120,\n    valueFormatter: params => {\n      var _params$row$category2;\n      return `${params.value} ${((_params$row$category2 = params.row.category) === null || _params$row$category2 === void 0 ? void 0 : _params$row$category2.unitLabel) || 'units'}`;\n    }\n  }, {\n    field: 'minStockLevel',\n    headerName: 'Min Stock',\n    width: 100,\n    valueFormatter: params => `${params.value} ${params.row.category.unitLabel}`\n  }, {\n    field: 'stockStatus',\n    headerName: 'Stock Status',\n    width: 120,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Chip, {\n      label: params.value,\n      color: getStockStatusColor(params.value),\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'isFinishedProduct',\n    headerName: 'Type',\n    width: 120,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Chip, {\n      label: params.value ? 'Finished' : 'Raw Material',\n      variant: \"outlined\",\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'actions',\n    type: 'actions',\n    headerName: 'Actions',\n    width: 100,\n    getActions: params => [/*#__PURE__*/_jsxDEV(GridActionsCellItem, {\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 17\n      }, this),\n      label: \"Edit\",\n      onClick: () => openEditDialog(params.row)\n    }, \"edit\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 9\n    }, this)]\n  }];\n\n  // Calculate summary statistics\n  const lowStockCount = products.filter(p => p.stockStatus === 'low').length;\n  const outOfStockCount = products.filter(p => p.stockStatus === 'out').length;\n  const totalProducts = products.length;\n  const totalValue = products.reduce((sum, p) => sum + p.currentStock * p.price, 0);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Products Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: totalProducts\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Total Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                color: \"warning\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: lowStockCount\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Low Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n                color: \"error\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: outOfStockCount\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Out of Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n                color: \"success\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [\"$\", totalValue.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Total Inventory Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.category,\n              label: \"Category\",\n              onChange: e => setFilters({\n                ...filters,\n                category: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), categories.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: cat._id,\n                children: cat.name\n              }, cat._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Stock Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.stockStatus,\n              label: \"Stock Status\",\n              onChange: e => setFilters({\n                ...filters,\n                stockStatus: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"All Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"low\",\n                children: \"Low Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"out\",\n                children: \"Out of Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"normal\",\n                children: \"Normal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Product Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.isFinishedProduct,\n              label: \"Product Type\",\n              onChange: e => setFilters({\n                ...filters,\n                isFinishedProduct: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"All Types\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"true\",\n                children: \"Finished Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"false\",\n                children: \"Raw Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 26\n            }, this),\n            onClick: openCreateDialog,\n            fullWidth: true,\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        height: 600,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataGrid, {\n        rows: products,\n        columns: columns,\n        getRowId: row => row._id,\n        loading: loading,\n        pageSizeOptions: [25, 50, 100],\n        initialState: {\n          pagination: {\n            paginationModel: {\n              page: 0,\n              pageSize: 25\n            }\n          }\n        },\n        disableRowSelectionOnClick: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProduct ? 'Edit Product' : 'Create New Product'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Product Name\",\n              value: formData.name,\n              onChange: e => setFormData({\n                ...formData,\n                name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.category,\n                label: \"Category\",\n                onChange: e => setFormData({\n                  ...formData,\n                  category: e.target.value\n                }),\n                children: categories.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat._id,\n                  children: [cat.name, \" (\", cat.unitLabel, \")\"]\n                }, cat._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Price\",\n              type: \"number\",\n              value: formData.price,\n              onChange: e => setFormData({\n                ...formData,\n                price: parseFloat(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Current Stock\",\n              type: \"number\",\n              value: formData.currentStock,\n              onChange: e => setFormData({\n                ...formData,\n                currentStock: parseFloat(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Min Stock Level\",\n              type: \"number\",\n              value: formData.minStockLevel,\n              onChange: e => setFormData({\n                ...formData,\n                minStockLevel: parseFloat(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Product Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.isFinishedProduct,\n                label: \"Product Type\",\n                onChange: e => setFormData({\n                  ...formData,\n                  isFinishedProduct: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: false,\n                  children: \"Raw Material\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: true,\n                  children: \"Finished Product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenDialog(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: selectedProduct ? handleUpdateProduct : handleCreateProduct,\n          variant: \"contained\",\n          children: selectedProduct ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsTab, \"roPdDBOJkIybe3Btr8lvFV0Xges=\");\n_c = ProductsTab;\nexport default ProductsTab;\nvar _c;\n$RefreshReg$(_c, \"ProductsTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "TextField", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "<PERSON><PERSON>", "Grid", "Add", "AddIcon", "Warning", "WarningIcon", "TrendingDown", "TrendingDownIcon", "Inventory", "InventoryIcon", "DataGrid", "GridActionsCellItem", "productsAPI", "categoriesAPI", "jsxDEV", "_jsxDEV", "ProductsTab", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedProduct", "setSelectedProduct", "filters", "setFilters", "category", "stockStatus", "isFinishedProduct", "formData", "setFormData", "name", "price", "currentStock", "minStockLevel", "fetchProducts", "fetchCategories", "response", "getAll", "data", "err", "_err$response", "_err$response$data", "message", "console", "handleCreateProduct", "create", "resetForm", "_err$response2", "_err$response2$data", "handleUpdateProduct", "update", "_id", "_err$response3", "_err$response3$data", "openCreateDialog", "openEditDialog", "product", "_product$category", "getStockStatusColor", "status", "columns", "field", "headerName", "width", "valueGetter", "params", "_params$row$category", "row", "valueFormatter", "value", "toFixed", "_params$row$category2", "unitLabel", "renderCell", "label", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "type", "getActions", "icon", "onClick", "lowStockCount", "filter", "p", "length", "outOfStockCount", "totalProducts", "totalValue", "reduce", "sum", "children", "gutterBottom", "container", "spacing", "sx", "mb", "item", "xs", "sm", "md", "display", "alignItems", "mr", "fullWidth", "onChange", "e", "target", "map", "cat", "startIcon", "severity", "onClose", "height", "rows", "getRowId", "pageSizeOptions", "initialState", "pagination", "paginationModel", "page", "pageSize", "disableRowSelectionOnClick", "open", "max<PERSON><PERSON><PERSON>", "mt", "parseFloat", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/JusSamy/frontend/src/components/ProductsTab.tsx"], "sourcesContent": ["// @ts-nocheck\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  <PERSON>ton,\n  Card,\n  CardContent,\n  Chip,\n  TextField,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  Alert,\n  CircularProgress,\n  Grid\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Warning as WarningIcon,\n  TrendingDown as TrendingDownIcon,\n  Inventory as InventoryIcon\n} from '@mui/icons-material';\nimport { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';\nimport { productsAPI, categoriesAPI } from '../services/api';\n\ninterface Product {\n  _id: string;\n  name: string;\n  category: {\n    _id: string;\n    name: string;\n    unitType: string;\n    unitLabel: string;\n  };\n  price: number;\n  currentStock: number;\n  minStockLevel: number;\n  stockStatus: 'low' | 'normal' | 'high' | 'out';\n  isActive: boolean;\n  isFinishedProduct: boolean;\n}\n\ninterface Category {\n  _id: string;\n  name: string;\n  unitType: string;\n  unitLabel: string;\n}\n\nconst ProductsTab: React.FC = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n  const [filters, setFilters] = useState({\n    category: '',\n    stockStatus: '',\n    isFinishedProduct: ''\n  });\n\n  // Form state for product creation/editing\n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    price: 0,\n    currentStock: 0,\n    minStockLevel: 0,\n    isFinishedProduct: false\n  });\n\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [filters]);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll(filters);\n      setProducts(response.data.products);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to fetch products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await categoriesAPI.getAll();\n      setCategories(response.data.categories);\n    } catch (err: any) {\n      console.error('Failed to fetch categories:', err);\n    }\n  };\n\n  const handleCreateProduct = async () => {\n    try {\n      await productsAPI.create(formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchProducts();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create product');\n    }\n  };\n\n  const handleUpdateProduct = async () => {\n    if (!selectedProduct) return;\n    \n    try {\n      await productsAPI.update(selectedProduct._id, formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchProducts();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to update product');\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      category: '',\n      price: 0,\n      currentStock: 0,\n      minStockLevel: 0,\n      isFinishedProduct: false\n    });\n    setSelectedProduct(null);\n  };\n\n  const openCreateDialog = () => {\n    resetForm();\n    setOpenDialog(true);\n  };\n\n  const openEditDialog = (product: Product) => {\n    setSelectedProduct(product);\n    setFormData({\n      name: product.name,\n      category: product.category?._id || '',\n      price: product.price,\n      currentStock: product.currentStock,\n      minStockLevel: product.minStockLevel,\n      isFinishedProduct: product.isFinishedProduct\n    });\n    setOpenDialog(true);\n  };\n\n  const getStockStatusColor = (status: string) => {\n    switch (status) {\n      case 'low': return 'warning';\n      case 'out': return 'error';\n      case 'high': return 'info';\n      default: return 'success';\n    }\n  };\n\n  const columns: GridColDef[] = [\n    { field: 'name', headerName: 'Product Name', width: 200 },\n    {\n      field: 'category',\n      headerName: 'Category',\n      width: 150,\n      valueGetter: (params: any) => params.row.category?.name || 'No Category'\n    },\n    {\n      field: 'price',\n      headerName: 'Price',\n      width: 100,\n      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`\n    },\n    {\n      field: 'currentStock',\n      headerName: 'Current Stock',\n      width: 120,\n      valueFormatter: (params: any) => `${params.value} ${params.row.category?.unitLabel || 'units'}`\n    },\n    {\n      field: 'minStockLevel',\n      headerName: 'Min Stock',\n      width: 100,\n      valueFormatter: (params: any) => `${params.value} ${params.row.category.unitLabel}`\n    },\n    {\n      field: 'stockStatus',\n      headerName: 'Stock Status',\n      width: 120,\n      renderCell: (params: any) => (\n        <Chip\n          label={params.value}\n          color={getStockStatusColor(params.value) as any}\n          size=\"small\"\n        />\n      )\n    },\n    {\n      field: 'isFinishedProduct',\n      headerName: 'Type',\n      width: 120,\n      renderCell: (params: any) => (\n        <Chip\n          label={params.value ? 'Finished' : 'Raw Material'}\n          variant=\"outlined\"\n          size=\"small\"\n        />\n      )\n    },\n    {\n      field: 'actions',\n      type: 'actions',\n      headerName: 'Actions',\n      width: 100,\n      getActions: (params: any) => [\n        <GridActionsCellItem\n          icon={<AddIcon />}\n          label=\"Edit\"\n          onClick={() => openEditDialog(params.row)}\n          key=\"edit\"\n        />\n      ]\n    }\n  ];\n\n  // Calculate summary statistics\n  const lowStockCount = products.filter(p => p.stockStatus === 'low').length;\n  const outOfStockCount = products.filter(p => p.stockStatus === 'out').length;\n  const totalProducts = products.length;\n  const totalValue = products.reduce((sum, p) => sum + (p.currentStock * p.price), 0);\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Products Management\n      </Typography>\n\n      {/* Summary Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <InventoryIcon color=\"primary\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">{totalProducts}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Total Products\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <WarningIcon color=\"warning\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">{lowStockCount}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Low Stock\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <TrendingDownIcon color=\"error\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">{outOfStockCount}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Out of Stock\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <InventoryIcon color=\"success\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">${totalValue.toFixed(2)}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Total Inventory Value\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Filters and Actions */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel>Category</InputLabel>\n              <Select\n                value={filters.category}\n                label=\"Category\"\n                onChange={(e) => setFilters({ ...filters, category: e.target.value })}\n              >\n                <MenuItem value=\"\">All Categories</MenuItem>\n                {categories.map((cat) => (\n                  <MenuItem key={cat._id} value={cat._id}>\n                    {cat.name}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel>Stock Status</InputLabel>\n              <Select\n                value={filters.stockStatus}\n                label=\"Stock Status\"\n                onChange={(e) => setFilters({ ...filters, stockStatus: e.target.value })}\n              >\n                <MenuItem value=\"\">All Status</MenuItem>\n                <MenuItem value=\"low\">Low Stock</MenuItem>\n                <MenuItem value=\"out\">Out of Stock</MenuItem>\n                <MenuItem value=\"normal\">Normal</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel>Product Type</InputLabel>\n              <Select\n                value={filters.isFinishedProduct}\n                label=\"Product Type\"\n                onChange={(e) => setFilters({ ...filters, isFinishedProduct: e.target.value })}\n              >\n                <MenuItem value=\"\">All Types</MenuItem>\n                <MenuItem value=\"true\">Finished Products</MenuItem>\n                <MenuItem value=\"false\">Raw Materials</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={openCreateDialog}\n              fullWidth\n            >\n              Add Product\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Products Data Grid */}\n      <Paper sx={{ height: 600, width: '100%' }}>\n        <DataGrid\n          rows={products}\n          columns={columns}\n          getRowId={(row) => row._id}\n          loading={loading}\n          pageSizeOptions={[25, 50, 100]}\n          initialState={{\n            pagination: {\n              paginationModel: { page: 0, pageSize: 25 },\n            },\n          }}\n          disableRowSelectionOnClick\n        />\n      </Paper>\n\n      {/* Create/Edit Product Dialog */}\n      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {selectedProduct ? 'Edit Product' : 'Create New Product'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Product Name\"\n                value={formData.name}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <FormControl fullWidth>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={formData.category}\n                  label=\"Category\"\n                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n                >\n                  {categories.map((cat) => (\n                    <MenuItem key={cat._id} value={cat._id}>\n                      {cat.name} ({cat.unitLabel})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Price\"\n                type=\"number\"\n                value={formData.price}\n                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Current Stock\"\n                type=\"number\"\n                value={formData.currentStock}\n                onChange={(e) => setFormData({ ...formData, currentStock: parseFloat(e.target.value) || 0 })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Min Stock Level\"\n                type=\"number\"\n                value={formData.minStockLevel}\n                onChange={(e) => setFormData({ ...formData, minStockLevel: parseFloat(e.target.value) || 0 })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <FormControl fullWidth>\n                <InputLabel>Product Type</InputLabel>\n                <Select\n                  value={formData.isFinishedProduct}\n                  label=\"Product Type\"\n                  onChange={(e) => setFormData({ ...formData, isFinishedProduct: e.target.value as boolean })}\n                >\n                  <MenuItem value={false}>Raw Material</MenuItem>\n                  <MenuItem value={true}>Finished Product</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>\n          <Button \n            onClick={selectedProduct ? handleUpdateProduct : handleCreateProduct}\n            variant=\"contained\"\n          >\n            {selectedProduct ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ProductsTab;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EAELC,IAAI,QACC,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,OAAO,IAAIC,WAAW,EACtBC,YAAY,IAAIC,gBAAgB,EAChCC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,QAAQ,EAAcC,mBAAmB,QAAQ,kBAAkB;AAC5E,SAASC,WAAW,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0B7D,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC;IACrCkD,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC;IACvCuD,IAAI,EAAE,EAAE;IACRL,QAAQ,EAAE,EAAE;IACZM,KAAK,EAAE,CAAC;IACRC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBN,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFnD,SAAS,CAAC,MAAM;IACd0D,aAAa,CAAC,CAAC;IACfC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACZ,OAAO,CAAC,CAAC;EAEb,MAAMW,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAM/B,WAAW,CAACgC,MAAM,CAACd,OAAO,CAAC;MAClDX,WAAW,CAACwB,QAAQ,CAACE,IAAI,CAAC3B,QAAQ,CAAC;IACrC,CAAC,CAAC,OAAO4B,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBvB,QAAQ,CAAC,EAAAsB,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,0BAA0B,CAAC;IACrE,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,aAAa,CAAC+B,MAAM,CAAC,CAAC;MAC7CvB,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAACzB,UAAU,CAAC;IACzC,CAAC,CAAC,OAAO0B,GAAQ,EAAE;MACjBI,OAAO,CAAC1B,KAAK,CAAC,6BAA6B,EAAEsB,GAAG,CAAC;IACnD;EACF,CAAC;EAED,MAAMK,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMvC,WAAW,CAACwC,MAAM,CAACjB,QAAQ,CAAC;MAClCR,aAAa,CAAC,KAAK,CAAC;MACpB0B,SAAS,CAAC,CAAC;MACXZ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOK,GAAQ,EAAE;MAAA,IAAAQ,cAAA,EAAAC,mBAAA;MACjB9B,QAAQ,CAAC,EAAA6B,cAAA,GAAAR,GAAG,CAACH,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcT,IAAI,cAAAU,mBAAA,uBAAlBA,mBAAA,CAAoBN,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC5B,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMhB,WAAW,CAAC6C,MAAM,CAAC7B,eAAe,CAAC8B,GAAG,EAAEvB,QAAQ,CAAC;MACvDR,aAAa,CAAC,KAAK,CAAC;MACpB0B,SAAS,CAAC,CAAC;MACXZ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOK,GAAQ,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACjBnC,QAAQ,CAAC,EAAAkC,cAAA,GAAAb,GAAG,CAACH,QAAQ,cAAAgB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,uBAAlBA,mBAAA,CAAoBX,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtBjB,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRL,QAAQ,EAAE,EAAE;MACZM,KAAK,EAAE,CAAC;MACRC,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC;MAChBN,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFL,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,SAAS,CAAC,CAAC;IACX1B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmC,cAAc,GAAIC,OAAgB,IAAK;IAAA,IAAAC,iBAAA;IAC3CnC,kBAAkB,CAACkC,OAAO,CAAC;IAC3B3B,WAAW,CAAC;MACVC,IAAI,EAAE0B,OAAO,CAAC1B,IAAI;MAClBL,QAAQ,EAAE,EAAAgC,iBAAA,GAAAD,OAAO,CAAC/B,QAAQ,cAAAgC,iBAAA,uBAAhBA,iBAAA,CAAkBN,GAAG,KAAI,EAAE;MACrCpB,KAAK,EAAEyB,OAAO,CAACzB,KAAK;MACpBC,YAAY,EAAEwB,OAAO,CAACxB,YAAY;MAClCC,aAAa,EAAEuB,OAAO,CAACvB,aAAa;MACpCN,iBAAiB,EAAE6B,OAAO,CAAC7B;IAC7B,CAAC,CAAC;IACFP,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMsC,mBAAmB,GAAIC,MAAc,IAAK;IAC9C,QAAQA,MAAM;MACZ,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,OAAqB,GAAG,CAC5B;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAI,CAAC,EACzD;IACEF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE,GAAG;IACVC,WAAW,EAAGC,MAAW;MAAA,IAAAC,oBAAA;MAAA,OAAK,EAAAA,oBAAA,GAAAD,MAAM,CAACE,GAAG,CAAC1C,QAAQ,cAAAyC,oBAAA,uBAAnBA,oBAAA,CAAqBpC,IAAI,KAAI,aAAa;IAAA;EAC1E,CAAC,EACD;IACE+B,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,GAAG;IACVK,cAAc,EAAGH,MAAW,IAAK,IAAIA,MAAM,CAACI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,EACD;IACET,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,GAAG;IACVK,cAAc,EAAGH,MAAW;MAAA,IAAAM,qBAAA;MAAA,OAAK,GAAGN,MAAM,CAACI,KAAK,IAAI,EAAAE,qBAAA,GAAAN,MAAM,CAACE,GAAG,CAAC1C,QAAQ,cAAA8C,qBAAA,uBAAnBA,qBAAA,CAAqBC,SAAS,KAAI,OAAO,EAAE;IAAA;EACjG,CAAC,EACD;IACEX,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,WAAW;IACvBC,KAAK,EAAE,GAAG;IACVK,cAAc,EAAGH,MAAW,IAAK,GAAGA,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACE,GAAG,CAAC1C,QAAQ,CAAC+C,SAAS;EACnF,CAAC,EACD;IACEX,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE,cAAc;IAC1BC,KAAK,EAAE,GAAG;IACVU,UAAU,EAAGR,MAAW,iBACtBzD,OAAA,CAACzB,IAAI;MACH2F,KAAK,EAAET,MAAM,CAACI,KAAM;MACpBM,KAAK,EAAEjB,mBAAmB,CAACO,MAAM,CAACI,KAAK,CAAS;MAChDO,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,GAAG;IACVU,UAAU,EAAGR,MAAW,iBACtBzD,OAAA,CAACzB,IAAI;MACH2F,KAAK,EAAET,MAAM,CAACI,KAAK,GAAG,UAAU,GAAG,cAAe;MAClDY,OAAO,EAAC,UAAU;MAClBL,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,SAAS;IAChBqB,IAAI,EAAE,SAAS;IACfpB,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,GAAG;IACVoB,UAAU,EAAGlB,MAAW,IAAK,cAC3BzD,OAAA,CAACJ,mBAAmB;MAClBgF,IAAI,eAAE5E,OAAA,CAACZ,OAAO;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAClBN,KAAK,EAAC,MAAM;MACZW,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAACU,MAAM,CAACE,GAAG;IAAE,GACtC,MAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEN,CAAC,CACF;;EAED;EACA,MAAMM,aAAa,GAAG3E,QAAQ,CAAC4E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,WAAW,KAAK,KAAK,CAAC,CAAC+D,MAAM;EAC1E,MAAMC,eAAe,GAAG/E,QAAQ,CAAC4E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,WAAW,KAAK,KAAK,CAAC,CAAC+D,MAAM;EAC5E,MAAME,aAAa,GAAGhF,QAAQ,CAAC8E,MAAM;EACrC,MAAMG,UAAU,GAAGjF,QAAQ,CAACkF,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,GAAIN,CAAC,CAACxD,YAAY,GAAGwD,CAAC,CAACzD,KAAM,EAAE,CAAC,CAAC;EAEnF,oBACEvB,OAAA,CAAC/B,GAAG;IAAAsH,QAAA,gBACFvF,OAAA,CAAC7B,UAAU;MAACsG,OAAO,EAAC,IAAI;MAACe,YAAY;MAAAD,QAAA,EAAC;IAEtC;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbxE,OAAA,CAACd,IAAI;MAACuG,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCvF,OAAA,CAACd,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BvF,OAAA,CAAC3B,IAAI;UAAAkH,QAAA,eACHvF,OAAA,CAAC1B,WAAW;YAAAiH,QAAA,eACVvF,OAAA,CAAC/B,GAAG;cAACgI,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCvF,OAAA,CAACN,aAAa;gBAACyE,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEQ,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDxE,OAAA,CAAC/B,GAAG;gBAAAsH,QAAA,gBACFvF,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,IAAI;kBAAAc,QAAA,EAAEJ;gBAAa;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrDxE,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,eAAe;kBAAAoB,QAAA,EAAC;gBAElD;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxE,OAAA,CAACd,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BvF,OAAA,CAAC3B,IAAI;UAAAkH,QAAA,eACHvF,OAAA,CAAC1B,WAAW;YAAAiH,QAAA,eACVvF,OAAA,CAAC/B,GAAG;cAACgI,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCvF,OAAA,CAACV,WAAW;gBAAC6E,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEQ,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CxE,OAAA,CAAC/B,GAAG;gBAAAsH,QAAA,gBACFvF,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,IAAI;kBAAAc,QAAA,EAAET;gBAAa;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrDxE,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,eAAe;kBAAAoB,QAAA,EAAC;gBAElD;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxE,OAAA,CAACd,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BvF,OAAA,CAAC3B,IAAI;UAAAkH,QAAA,eACHvF,OAAA,CAAC1B,WAAW;YAAAiH,QAAA,eACVvF,OAAA,CAAC/B,GAAG;cAACgI,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCvF,OAAA,CAACR,gBAAgB;gBAAC2E,KAAK,EAAC,OAAO;gBAACwB,EAAE,EAAE;kBAAEQ,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDxE,OAAA,CAAC/B,GAAG;gBAAAsH,QAAA,gBACFvF,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,IAAI;kBAAAc,QAAA,EAAEL;gBAAe;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACvDxE,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,eAAe;kBAAAoB,QAAA,EAAC;gBAElD;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxE,OAAA,CAACd,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BvF,OAAA,CAAC3B,IAAI;UAAAkH,QAAA,eACHvF,OAAA,CAAC1B,WAAW;YAAAiH,QAAA,eACVvF,OAAA,CAAC/B,GAAG;cAACgI,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCvF,OAAA,CAACN,aAAa;gBAACyE,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEQ,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDxE,OAAA,CAAC/B,GAAG;gBAAAsH,QAAA,gBACFvF,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,IAAI;kBAAAc,QAAA,GAAC,GAAC,EAACH,UAAU,CAACtB,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9DxE,OAAA,CAAC7B,UAAU;kBAACsG,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,eAAe;kBAAAoB,QAAA,EAAC;gBAElD;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPxE,OAAA,CAAC9B,KAAK;MAACyH,EAAE,EAAE;QAAEX,CAAC,EAAE,CAAC;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACzBvF,OAAA,CAACd,IAAI;QAACuG,SAAS;QAACC,OAAO,EAAE,CAAE;QAACQ,UAAU,EAAC,QAAQ;QAAAX,QAAA,gBAC7CvF,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eAC9BvF,OAAA,CAAClB,WAAW;YAACsH,SAAS;YAAChC,IAAI,EAAC,OAAO;YAAAmB,QAAA,gBACjCvF,OAAA,CAACjB,UAAU;cAAAwG,QAAA,EAAC;YAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjCxE,OAAA,CAAChB,MAAM;cACL6E,KAAK,EAAE9C,OAAO,CAACE,QAAS;cACxBiD,KAAK,EAAC,UAAU;cAChBmC,QAAQ,EAAGC,CAAC,IAAKtF,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEE,QAAQ,EAAEqF,CAAC,CAACC,MAAM,CAAC1C;cAAM,CAAC,CAAE;cAAA0B,QAAA,gBAEtEvF,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,EAAE;gBAAA0B,QAAA,EAAC;cAAc;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAC3CnE,UAAU,CAACmG,GAAG,CAAEC,GAAG,iBAClBzG,OAAA,CAACvB,QAAQ;gBAAeoF,KAAK,EAAE4C,GAAG,CAAC9D,GAAI;gBAAA4C,QAAA,EACpCkB,GAAG,CAACnF;cAAI,GADImF,GAAG,CAAC9D,GAAG;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPxE,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eAC9BvF,OAAA,CAAClB,WAAW;YAACsH,SAAS;YAAChC,IAAI,EAAC,OAAO;YAAAmB,QAAA,gBACjCvF,OAAA,CAACjB,UAAU;cAAAwG,QAAA,EAAC;YAAY;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCxE,OAAA,CAAChB,MAAM;cACL6E,KAAK,EAAE9C,OAAO,CAACG,WAAY;cAC3BgD,KAAK,EAAC,cAAc;cACpBmC,QAAQ,EAAGC,CAAC,IAAKtF,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEG,WAAW,EAAEoF,CAAC,CAACC,MAAM,CAAC1C;cAAM,CAAC,CAAE;cAAA0B,QAAA,gBAEzEvF,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,EAAE;gBAAA0B,QAAA,EAAC;cAAU;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCxE,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAS;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1CxE,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAY;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CxE,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,QAAQ;gBAAA0B,QAAA,EAAC;cAAM;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPxE,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eAC9BvF,OAAA,CAAClB,WAAW;YAACsH,SAAS;YAAChC,IAAI,EAAC,OAAO;YAAAmB,QAAA,gBACjCvF,OAAA,CAACjB,UAAU;cAAAwG,QAAA,EAAC;YAAY;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCxE,OAAA,CAAChB,MAAM;cACL6E,KAAK,EAAE9C,OAAO,CAACI,iBAAkB;cACjC+C,KAAK,EAAC,cAAc;cACpBmC,QAAQ,EAAGC,CAAC,IAAKtF,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEI,iBAAiB,EAAEmF,CAAC,CAACC,MAAM,CAAC1C;cAAM,CAAC,CAAE;cAAA0B,QAAA,gBAE/EvF,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,EAAE;gBAAA0B,QAAA,EAAC;cAAS;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvCxE,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,MAAM;gBAAA0B,QAAA,EAAC;cAAiB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACnDxE,OAAA,CAACvB,QAAQ;gBAACoF,KAAK,EAAC,OAAO;gBAAA0B,QAAA,EAAC;cAAa;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPxE,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eAC9BvF,OAAA,CAAC5B,MAAM;YACLqG,OAAO,EAAC,WAAW;YACnBiC,SAAS,eAAE1G,OAAA,CAACZ,OAAO;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBK,OAAO,EAAE/B,gBAAiB;YAC1BsD,SAAS;YAAAb,QAAA,EACV;UAED;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP/D,KAAK,iBACJT,OAAA,CAACf,KAAK;MAAC0H,QAAQ,EAAC,OAAO;MAAChB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAACgB,OAAO,EAAEA,CAAA,KAAMlG,QAAQ,CAAC,IAAI,CAAE;MAAA6E,QAAA,EAClE9E;IAAK;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDxE,OAAA,CAAC9B,KAAK;MAACyH,EAAE,EAAE;QAAEkB,MAAM,EAAE,GAAG;QAAEtD,KAAK,EAAE;MAAO,CAAE;MAAAgC,QAAA,eACxCvF,OAAA,CAACL,QAAQ;QACPmH,IAAI,EAAE3G,QAAS;QACfiD,OAAO,EAAEA,OAAQ;QACjB2D,QAAQ,EAAGpD,GAAG,IAAKA,GAAG,CAAChB,GAAI;QAC3BpC,OAAO,EAAEA,OAAQ;QACjByG,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QAC/BC,YAAY,EAAE;UACZC,UAAU,EAAE;YACVC,eAAe,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAG;UAC3C;QACF,CAAE;QACFC,0BAA0B;MAAA;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRxE,OAAA,CAACtB,MAAM;MAAC6I,IAAI,EAAE5G,UAAW;MAACiG,OAAO,EAAEA,CAAA,KAAMhG,aAAa,CAAC,KAAK,CAAE;MAAC4G,QAAQ,EAAC,IAAI;MAACpB,SAAS;MAAAb,QAAA,gBACpFvF,OAAA,CAACrB,WAAW;QAAA4G,QAAA,EACT1E,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdxE,OAAA,CAACpB,aAAa;QAAA2G,QAAA,eACZvF,OAAA,CAACd,IAAI;UAACuG,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACxCvF,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAP,QAAA,eAChBvF,OAAA,CAACxB,SAAS;cACR4H,SAAS;cACTlC,KAAK,EAAC,cAAc;cACpBL,KAAK,EAAEzC,QAAQ,CAACE,IAAK;cACrB+E,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,IAAI,EAAEgF,CAAC,CAACC,MAAM,CAAC1C;cAAM,CAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxE,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAP,QAAA,eAChBvF,OAAA,CAAClB,WAAW;cAACsH,SAAS;cAAAb,QAAA,gBACpBvF,OAAA,CAACjB,UAAU;gBAAAwG,QAAA,EAAC;cAAQ;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCxE,OAAA,CAAChB,MAAM;gBACL6E,KAAK,EAAEzC,QAAQ,CAACH,QAAS;gBACzBiD,KAAK,EAAC,UAAU;gBAChBmC,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEH,QAAQ,EAAEqF,CAAC,CAACC,MAAM,CAAC1C;gBAAM,CAAC,CAAE;gBAAA0B,QAAA,EAEvElF,UAAU,CAACmG,GAAG,CAAEC,GAAG,iBAClBzG,OAAA,CAACvB,QAAQ;kBAAeoF,KAAK,EAAE4C,GAAG,CAAC9D,GAAI;kBAAA4C,QAAA,GACpCkB,GAAG,CAACnF,IAAI,EAAC,IAAE,EAACmF,GAAG,CAACzC,SAAS,EAAC,GAC7B;gBAAA,GAFeyC,GAAG,CAAC9D,GAAG;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPxE,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfvF,OAAA,CAACxB,SAAS;cACR4H,SAAS;cACTlC,KAAK,EAAC,OAAO;cACbQ,IAAI,EAAC,QAAQ;cACbb,KAAK,EAAEzC,QAAQ,CAACG,KAAM;cACtB8E,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,KAAK,EAAEmG,UAAU,CAACpB,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC,IAAI;cAAE,CAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxE,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfvF,OAAA,CAACxB,SAAS;cACR4H,SAAS;cACTlC,KAAK,EAAC,eAAe;cACrBQ,IAAI,EAAC,QAAQ;cACbb,KAAK,EAAEzC,QAAQ,CAACI,YAAa;cAC7B6E,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAEkG,UAAU,CAACpB,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC,IAAI;cAAE,CAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxE,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfvF,OAAA,CAACxB,SAAS;cACR4H,SAAS;cACTlC,KAAK,EAAC,iBAAiB;cACvBQ,IAAI,EAAC,QAAQ;cACbb,KAAK,EAAEzC,QAAQ,CAACK,aAAc;cAC9B4E,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAEiG,UAAU,CAACpB,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC,IAAI;cAAE,CAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxE,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfvF,OAAA,CAAClB,WAAW;cAACsH,SAAS;cAAAb,QAAA,gBACpBvF,OAAA,CAACjB,UAAU;gBAAAwG,QAAA,EAAC;cAAY;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxE,OAAA,CAAChB,MAAM;gBACL6E,KAAK,EAAEzC,QAAQ,CAACD,iBAAkB;gBAClC+C,KAAK,EAAC,cAAc;gBACpBmC,QAAQ,EAAGC,CAAC,IAAKjF,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAED,iBAAiB,EAAEmF,CAAC,CAACC,MAAM,CAAC1C;gBAAiB,CAAC,CAAE;gBAAA0B,QAAA,gBAE5FvF,OAAA,CAACvB,QAAQ;kBAACoF,KAAK,EAAE,KAAM;kBAAA0B,QAAA,EAAC;gBAAY;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC/CxE,OAAA,CAACvB,QAAQ;kBAACoF,KAAK,EAAE,IAAK;kBAAA0B,QAAA,EAAC;gBAAgB;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBxE,OAAA,CAACnB,aAAa;QAAA0G,QAAA,gBACZvF,OAAA,CAAC5B,MAAM;UAACyG,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAAC,KAAK,CAAE;UAAA2E,QAAA,EAAC;QAAM;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5DxE,OAAA,CAAC5B,MAAM;UACLyG,OAAO,EAAEhE,eAAe,GAAG4B,mBAAmB,GAAGL,mBAAoB;UACrEqC,OAAO,EAAC,WAAW;UAAAc,QAAA,EAElB1E,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtE,EAAA,CAzaID,WAAqB;AAAA0H,EAAA,GAArB1H,WAAqB;AA2a3B,eAAeA,WAAW;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}