// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Factory as FactoryIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { factoryAPI, productsAPI } from '../services/api';
import { format } from 'date-fns';

interface FactoryLog {
  _id: string;
  date: string;
  type: 'production' | 'consumption' | 'waste';
  batchNumber: string;
  materialsUsed: Array<{
    product: { _id: string; name: string };
    quantity: number;
    unit: string;
    cost: number;
  }>;
  productsGenerated: Array<{
    product: { _id: string; name: string };
    quantity: number;
    unit: string;
    estimatedValue: number;
  }>;
  efficiency: number;
  totalCost: number;
  totalValue: number;
  profitLoss: number;
  operator: string;
  shift: string;
  notes: string;
}

interface Product {
  _id: string;
  name: string;
  currentStock: number;
}

const FactoryTab: React.FC = () => {
  const [logs, setLogs] = useState<FactoryLog[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [summary, setSummary] = useState<any>(null);
  const [efficiency, setEfficiency] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  // const [selectedLog, setSelectedLog] = useState<FactoryLog | null>(null);

  // Form state for factory log creation/editing
  const [formData, setFormData] = useState({
    type: 'production' as 'production' | 'consumption' | 'waste',
    batchNumber: '',
    materialsUsed: [{ product: '', quantity: 0, unit: '', cost: 0 }],
    productsGenerated: [{ product: '', quantity: 0, unit: '', estimatedValue: 0 }],
    operator: '',
    shift: 'morning' as 'morning' | 'afternoon' | 'night',
    notes: '',
    efficiency: 0
  });

  useEffect(() => {
    fetchLogs();
    fetchProducts();
    fetchSummary();
    fetchEfficiency();
  }, []);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const response = await factoryAPI.getLogs();
      setLogs(response.data.logs);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch factory logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await productsAPI.getAll();
      setProducts(response.data.products);
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
    }
  };

  const fetchSummary = async () => {
    try {
      const response = await factoryAPI.getSummary();
      setSummary(response.data.summary);
    } catch (err: any) {
      console.error('Failed to fetch summary:', err);
    }
  };

  const fetchEfficiency = async () => {
    try {
      const response = await factoryAPI.getEfficiency();
      setEfficiency(response.data);
    } catch (err: any) {
      console.error('Failed to fetch efficiency:', err);
    }
  };

  const handleCreateLog = async () => {
    try {
      await factoryAPI.createLog(formData);
      setOpenDialog(false);
      resetForm();
      fetchLogs();
      fetchSummary();
      fetchEfficiency();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create factory log');
    }
  };

  const resetForm = () => {
    setFormData({
      type: 'production',
      batchNumber: '',
      materialsUsed: [{ product: '', quantity: 0, unit: '', cost: 0 }],
      productsGenerated: [{ product: '', quantity: 0, unit: '', estimatedValue: 0 }],
      operator: '',
      shift: 'morning',
      notes: '',
      efficiency: 0
    });
    // setSelectedLog(null);
  };

  const addMaterialRow = () => {
    setFormData({
      ...formData,
      materialsUsed: [...formData.materialsUsed, { product: '', quantity: 0, unit: '', cost: 0 }]
    });
  };

  const addProductRow = () => {
    setFormData({
      ...formData,
      productsGenerated: [...formData.productsGenerated, { product: '', quantity: 0, unit: '', estimatedValue: 0 }]
    });
  };

  const updateMaterial = (index: number, field: string, value: any) => {
    const updated = [...formData.materialsUsed];
    updated[index] = { ...updated[index], [field]: value };
    setFormData({ ...formData, materialsUsed: updated });
  };

  const updateProduct = (index: number, field: string, value: any) => {
    const updated = [...formData.productsGenerated];
    updated[index] = { ...updated[index], [field]: value };
    setFormData({ ...formData, productsGenerated: updated });
  };

  const columns: GridColDef[] = [
    {
      field: 'date',
      headerName: 'Date',
      width: 120,
      valueFormatter: (params: any) => format(new Date(params.value), 'MMM dd, yyyy')
    },
    { field: 'batchNumber', headerName: 'Batch Number', width: 150 },
    {
      field: 'type',
      headerName: 'Type',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={params.value === 'production' ? 'success' : params.value === 'consumption' ? 'warning' : 'error'}
          size="small"
        />
      )
    },
    { field: 'operator', headerName: 'Operator', width: 150 },
    { field: 'shift', headerName: 'Shift', width: 100 },
    {
      field: 'efficiency',
      headerName: 'Efficiency (%)',
      width: 120,
      valueFormatter: (params: any) => `${params.value?.toFixed(1) || 0}%`
    },
    {
      field: 'totalCost',
      headerName: 'Total Cost',
      width: 120,
      valueFormatter: (params: any) => `$${params.value?.toFixed(2) || 0}`
    },
    {
      field: 'totalValue',
      headerName: 'Total Value',
      width: 120,
      valueFormatter: (params: any) => `$${params.value?.toFixed(2) || 0}`
    },
    {
      field: 'profitLoss',
      headerName: 'Profit/Loss',
      width: 120,
      valueFormatter: (params: any) => `$${params.value?.toFixed(2) || 0}`,
      renderCell: (params: any) => (
        <Typography color={params.value >= 0 ? 'success.main' : 'error.main'}>
          ${params.value?.toFixed(2) || 0}
        </Typography>
      )
    }
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Factory Production Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <FactoryIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{summary?.totalLogs || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Production Logs
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${summary?.totalValue?.toFixed(2) || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Production Value
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AssessmentIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{summary?.avgEfficiency?.toFixed(1) || 0}%</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Average Efficiency
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color={summary?.totalProfit >= 0 ? 'success' : 'error'} sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6" color={summary?.totalProfit >= 0 ? 'success.main' : 'error.main'}>
                    ${summary?.totalProfit?.toFixed(2) || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Profit/Loss
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Daily Efficiency Trend
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={efficiency}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="avgEfficiency" stroke="#8884d8" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Daily Production Value
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={efficiency}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="totalValue" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          Add Production Log
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Factory Logs Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={logs}
          columns={columns}
          getRowId={(row) => row._id}
          loading={loading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Create Factory Log Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Production Log</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                >
                  <MenuItem value="production">Production</MenuItem>
                  <MenuItem value="consumption">Consumption</MenuItem>
                  <MenuItem value="waste">Waste</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Batch Number"
                value={formData.batchNumber}
                onChange={(e) => setFormData({ ...formData, batchNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Operator"
                value={formData.operator}
                onChange={(e) => setFormData({ ...formData, operator: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Shift</InputLabel>
                <Select
                  value={formData.shift}
                  label="Shift"
                  onChange={(e) => setFormData({ ...formData, shift: e.target.value as any })}
                >
                  <MenuItem value="morning">Morning</MenuItem>
                  <MenuItem value="afternoon">Afternoon</MenuItem>
                  <MenuItem value="night">Night</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Efficiency (%)"
                type="number"
                value={formData.efficiency}
                onChange={(e) => setFormData({ ...formData, efficiency: parseFloat(e.target.value) || 0 })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>

          {/* Materials Used Section */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Materials Used</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {formData.materialsUsed.map((material, index) => (
                <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Product</InputLabel>
                      <Select
                        value={material.product}
                        label="Product"
                        onChange={(e) => updateMaterial(index, 'product', e.target.value)}
                      >
                        {products.map((product) => (
                          <MenuItem key={product._id} value={product._id}>
                            {product.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Quantity"
                      type="number"
                      value={material.quantity}
                      onChange={(e) => updateMaterial(index, 'quantity', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Unit"
                      value={material.unit}
                      onChange={(e) => updateMaterial(index, 'unit', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Cost"
                      type="number"
                      value={material.cost}
                      onChange={(e) => updateMaterial(index, 'cost', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                </Grid>
              ))}
              <Button onClick={addMaterialRow} size="small">
                Add Material
              </Button>
            </AccordionDetails>
          </Accordion>

          {/* Products Generated Section */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Products Generated</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {formData.productsGenerated.map((product, index) => (
                <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Product</InputLabel>
                      <Select
                        value={product.product}
                        label="Product"
                        onChange={(e) => updateProduct(index, 'product', e.target.value)}
                      >
                        {products.map((prod) => (
                          <MenuItem key={prod._id} value={prod._id}>
                            {prod.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Quantity"
                      type="number"
                      value={product.quantity}
                      onChange={(e) => updateProduct(index, 'quantity', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Unit"
                      value={product.unit}
                      onChange={(e) => updateProduct(index, 'unit', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Estimated Value"
                      type="number"
                      value={product.estimatedValue}
                      onChange={(e) => updateProduct(index, 'estimatedValue', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                </Grid>
              ))}
              <Button onClick={addProductRow} size="small">
                Add Product
              </Button>
            </AccordionDetails>
          </Accordion>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateLog} variant="contained">
            Create Log
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FactoryTab;
