# JusSamy Unity Mobile App API Documentation

## Overview

This documentation provides comprehensive information for integrating the JusSamy inventory management system with Unity mobile applications. The API is designed specifically for real-time mobile app synchronization with optimized endpoints and webhook support.

## Base URL
```
http://localhost:5000/api/unity
```

## Authentication

All Unity API endpoints require an API key for authentication.

### Headers Required
```
X-API-Key: your-unity-api-key-here
```

### Environment Variable
Set `UNITY_API_KEY` in your backend environment configuration.

## Response Format

All Unity API responses follow this standardized format:

```json
{
  "success": true|false,
  "data": {...},
  "message": "Optional message",
  "timestamp": "2025-07-09T12:00:00.000Z",
  "count": 0
}
```

## Endpoints

### 1. Products Management

#### GET /api/unity/products
Get all products optimized for Unity consumption.

**Query Parameters:**
- `category` (optional): Filter by category ID
- `stockStatus` (optional): Filter by stock status (`low`, `out`, `normal`)
- `limit` (optional): Maximum number of products (default: 100)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "product_id",
      "name": "Apple Juice 1L",
      "categoryName": "Beverages",
      "unitType": "unit",
      "unitLabel": "Bottles",
      "price": 2.50,
      "currentStock": 45,
      "minStockLevel": 10,
      "stockStatus": "normal",
      "isFinishedProduct": true
    }
  ],
  "count": 1,
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

#### GET /api/unity/products/low-stock
Get products with low stock for Unity alerts.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "product_id",
      "name": "Apple Juice 1L",
      "categoryName": "Beverages",
      "currentStock": 5,
      "minStockLevel": 10,
      "unitLabel": "Bottles",
      "shortage": 5
    }
  ],
  "alertLevel": "warning",
  "count": 1,
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

### 2. Sales Management

#### POST /api/unity/sales
Record a new sale from Unity app.

**Request Body:**
```json
{
  "items": [
    {
      "productId": "product_id",
      "quantity": 2,
      "unitPrice": 2.50
    }
  ],
  "customer": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "paymentMethod": "cash",
  "salesPerson": "Unity App User"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "saleId": "sale_id",
    "saleNumber": "SALE-20250709-0001",
    "totalAmount": 5.00,
    "timestamp": "2025-07-09T12:00:00.000Z"
  },
  "message": "Sale recorded successfully"
}
```

### 3. Orders Management

#### POST /api/unity/orders
Create a new order from Unity app.

**Request Body:**
```json
{
  "customer": {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St, City, Country"
  },
  "items": [
    {
      "productId": "product_id",
      "quantity": 24,
      "unitPrice": 2.50
    }
  ],
  "deliveryDate": "2025-07-16",
  "priority": "normal",
  "notes": "Order from Unity mobile app"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_id",
    "orderNumber": "ORD-20250709-0001",
    "totalAmount": 60.00,
    "deliveryDate": "2025-07-16T00:00:00.000Z",
    "status": "pending",
    "stockAlert": {
      "isTriggered": false
    }
  },
  "message": "Order created successfully"
}
```

#### GET /api/unity/orders
Get orders for Unity app.

**Query Parameters:**
- `status` (optional): Filter by order status
- `limit` (optional): Maximum number of orders (default: 50)
- `customerPhone` (optional): Filter by customer phone number

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "order_id",
      "orderNumber": "ORD-20250709-0001",
      "orderDate": "2025-07-09T12:00:00.000Z",
      "deliveryDate": "2025-07-16T00:00:00.000Z",
      "status": "pending",
      "priority": "normal",
      "customerName": "Jane Smith",
      "customerPhone": "+1234567890",
      "totalAmount": 60.00,
      "daysUntilDelivery": 7,
      "urgencyLevel": "low"
    }
  ],
  "count": 1,
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

### 4. Factory Production

#### POST /api/unity/factory/log
Record production from Unity app.

**Request Body:**
```json
{
  "type": "production",
  "materialsUsed": [
    {
      "product": "material_product_id",
      "quantity": 100,
      "unit": "KG",
      "cost": 150.00
    }
  ],
  "productsGenerated": [
    {
      "product": "finished_product_id",
      "quantity": 75,
      "unit": "Bottles",
      "estimatedValue": 187.50
    }
  ],
  "operator": "Unity App User",
  "shift": "morning",
  "efficiency": 85.5,
  "notes": "Production logged from Unity app"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "logId": "log_id",
    "batchNumber": "UNITY-1720531200000",
    "totalCost": 150.00,
    "totalValue": 187.50,
    "profitLoss": 37.50
  },
  "message": "Production log recorded successfully"
}
```

### 5. Dashboard Data

#### GET /api/unity/dashboard
Get dashboard summary data for Unity app.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalProducts": 150,
      "lowStockAlerts": 5,
      "pendingOrders": 12,
      "todaySales": 8
    },
    "recentSales": [
      {
        "id": "sale_id",
        "saleNumber": "SALE-20250709-0001",
        "amount": 25.50,
        "date": "2025-07-09T12:00:00.000Z",
        "customer": "John Doe"
      }
    ],
    "urgentOrders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-20250709-0001",
        "customer": "Jane Smith",
        "deliveryDate": "2025-07-10T00:00:00.000Z",
        "amount": 60.00
      }
    ],
    "alertLevel": "normal"
  },
  "timestamp": "2025-07-09T12:00:00.000Z"
}
```

## Webhook Integration

### Unity-Optimized Webhook Events

The system provides Unity-specific webhook events optimized for mobile app notifications:

#### Event Types:
- `unity.stock.low` - Low stock alerts
- `unity.stock.critical` - Critical stock alerts
- `unity.order.urgent` - Urgent order notifications
- `unity.sale.completed` - Sale completion notifications
- `unity.production.completed` - Production completion
- `unity.sync.request` - Data synchronization requests

#### Webhook Payload Format:
```json
{
  "event": "unity.stock.low",
  "timestamp": "2025-07-09T12:00:00.000Z",
  "data": {
    "alertType": "stock_low",
    "severity": "warning",
    "products": [...],
    "actionRequired": true
  },
  "unity": {
    "notificationType": "alert",
    "priority": "medium",
    "displayDuration": 10000,
    "soundAlert": true
  }
}
```

### Setting Up Webhooks

1. **Create Webhook Endpoint** in your Unity backend service
2. **Configure Webhook** in JusSamy admin panel:
   ```json
   {
     "name": "Unity Mobile App",
     "url": "https://your-unity-backend.com/webhooks/jussamy",
     "events": ["unity.stock.low", "unity.order.urgent"],
     "secret": "your-webhook-secret"
   }
   ```

## Error Handling

### Error Response Format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

### Common HTTP Status Codes:
- `200` - Success
- `201` - Created successfully
- `400` - Bad request (validation errors)
- `401` - Unauthorized (invalid API key)
- `404` - Resource not found
- `500` - Internal server error

## Unity Implementation Examples

### C# HTTP Client Example:
```csharp
using UnityEngine;
using UnityEngine.Networking;
using System.Collections;

public class JusSamyAPI : MonoBehaviour
{
    private const string BASE_URL = "http://localhost:5000/api/unity";
    private const string API_KEY = "your-unity-api-key";

    public IEnumerator GetProducts()
    {
        using (UnityWebRequest request = UnityWebRequest.Get($"{BASE_URL}/products"))
        {
            request.SetRequestHeader("X-API-Key", API_KEY);
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                string jsonResponse = request.downloadHandler.text;
                ProductResponse response = JsonUtility.FromJson<ProductResponse>(jsonResponse);
                // Process products data
            }
        }
    }
}
```

### WebSocket Connection for Real-time Updates:
```csharp
// Connect to WebSocket for real-time updates
var socket = new WebSocket("ws://localhost:5000");
socket.OnMessage += (sender, e) => {
    // Handle real-time updates
    Debug.Log("Received update: " + e.Data);
};
```

## Rate Limiting

- **100 requests per minute** per API key
- **Burst limit**: 20 requests per 10 seconds
- Rate limit headers included in responses

## Best Practices

1. **Cache Data**: Cache product and category data locally
2. **Batch Operations**: Group multiple operations when possible
3. **Handle Offline**: Implement offline mode with sync when online
4. **Error Retry**: Implement exponential backoff for failed requests
5. **Webhook Verification**: Always verify webhook signatures
6. **Real-time Updates**: Use WebSocket for real-time inventory updates

## Support

For technical support and integration assistance:
- Email: <EMAIL>
- Documentation: https://docs.jussamy.com
- GitHub Issues: https://github.com/jussamy/unity-integration
