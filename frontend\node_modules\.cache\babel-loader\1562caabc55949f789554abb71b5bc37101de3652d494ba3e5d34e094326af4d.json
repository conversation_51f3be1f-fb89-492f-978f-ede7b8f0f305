{"ast": null, "code": "// @ts-nocheck\nimport axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for adding auth tokens if needed\napi.interceptors.request.use(config => {\n  // Add auth token if available\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for handling errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    localStorage.removeItem('authToken');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Categories API\nexport const categoriesAPI = {\n  getAll: (params = {}) => api.get('/categories', {\n    params\n  }),\n  getById: id => api.get(`/categories/${id}`),\n  create: data => api.post('/categories', data),\n  update: (id, data) => api.put(`/categories/${id}`, data),\n  delete: id => api.delete(`/categories/${id}`)\n};\n\n// Products API\nexport const productsAPI = {\n  getAll: (params = {}) => api.get('/products', {\n    params\n  }),\n  getById: id => api.get(`/products/${id}`),\n  getLowStock: () => api.get('/products/low-stock'),\n  create: data => api.post('/products', data),\n  update: (id, data) => api.put(`/products/${id}`, data),\n  updateStock: (id, data) => api.patch(`/products/${id}/stock`, data),\n  delete: id => api.delete(`/products/${id}`)\n};\n\n// Factory API\nexport const factoryAPI = {\n  getLogs: (params = {}) => api.get('/factory/logs', {\n    params\n  }),\n  getLogById: id => api.get(`/factory/logs/${id}`),\n  createLog: data => api.post('/factory/logs', data),\n  updateLog: (id, data) => api.put(`/factory/logs/${id}`, data),\n  deleteLog: id => api.delete(`/factory/logs/${id}`),\n  getSummary: (params = {}) => api.get('/factory/summary', {\n    params\n  }),\n  getEfficiency: (params = {}) => api.get('/factory/efficiency', {\n    params\n  })\n};\n\n// Sales API\nexport const salesAPI = {\n  getAll: (params = {}) => api.get('/sales', {\n    params\n  }),\n  getById: id => api.get(`/sales/${id}`),\n  create: data => api.post('/sales', data),\n  update: (id, data) => api.put(`/sales/${id}`, data),\n  delete: id => api.delete(`/sales/${id}`),\n  getDashboard: (params = {}) => api.get('/sales/summary/dashboard', {\n    params\n  }),\n  processRefund: (id, data) => api.post(`/sales/${id}/refund`, data)\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: (params = {}) => api.get('/orders', {\n    params\n  }),\n  getById: id => api.get(`/orders/${id}`),\n  getUrgent: () => api.get('/orders/urgent'),\n  create: data => api.post('/orders', data),\n  update: (id, data) => api.put(`/orders/${id}`, data),\n  updateStatus: (id, status) => api.patch(`/orders/${id}/status`, {\n    status\n  }),\n  delete: id => api.delete(`/orders/${id}`),\n  getDashboard: (params = {}) => api.get('/orders/summary/dashboard', {\n    params\n  }),\n  checkStock: id => api.post(`/orders/${id}/check-stock`)\n};\n\n// Webhooks API\nexport const webhooksAPI = {\n  getAll: (params = {}) => api.get('/webhooks', {\n    params\n  }),\n  getById: id => api.get(`/webhooks/${id}`),\n  create: data => api.post('/webhooks', data),\n  update: (id, data) => api.put(`/webhooks/${id}`, data),\n  delete: id => api.delete(`/webhooks/${id}`),\n  toggle: id => api.patch(`/webhooks/${id}/toggle`),\n  test: id => api.post(`/webhooks/${id}/test`),\n  getStats: id => api.get(`/webhooks/${id}/stats`),\n  getEventTypes: () => api.get('/webhooks/events/types'),\n  trigger: data => api.post('/webhooks/trigger', data),\n  getRecentLogs: (params = {}) => api.get('/webhooks/logs/recent', {\n    params\n  })\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => api.get('/health')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "categoriesAPI", "getAll", "params", "get", "getById", "id", "data", "post", "update", "put", "delete", "productsAPI", "getLowStock", "updateStock", "patch", "factoryAPI", "getLogs", "getLogById", "createLog", "updateLog", "deleteLog", "getSummary", "getEfficiency", "salesAPI", "getDashboard", "processRefund", "ordersAPI", "getUrgent", "updateStatus", "checkStock", "webhooksAPI", "toggle", "test", "getStats", "getEventTypes", "trigger", "getRecentLogs", "healthAPI", "check"], "sources": ["C:/Users/<USER>/Documents/augment-projects/JusSamy/frontend/src/services/api.ts"], "sourcesContent": ["// @ts-nocheck\nimport axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for adding auth tokens if needed\napi.interceptors.request.use(\n  (config) => {\n    // Add auth token if available\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for handling errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      localStorage.removeItem('authToken');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Categories API\nexport const categoriesAPI = {\n  getAll: (params = {}) => api.get('/categories', { params }),\n  getById: (id) => api.get(`/categories/${id}`),\n  create: (data) => api.post('/categories', data),\n  update: (id, data) => api.put(`/categories/${id}`, data),\n  delete: (id) => api.delete(`/categories/${id}`),\n};\n\n// Products API\nexport const productsAPI = {\n  getAll: (params = {}) => api.get('/products', { params }),\n  getById: (id) => api.get(`/products/${id}`),\n  getLowStock: () => api.get('/products/low-stock'),\n  create: (data) => api.post('/products', data),\n  update: (id, data) => api.put(`/products/${id}`, data),\n  updateStock: (id, data) => api.patch(`/products/${id}/stock`, data),\n  delete: (id) => api.delete(`/products/${id}`),\n};\n\n// Factory API\nexport const factoryAPI = {\n  getLogs: (params = {}) => api.get('/factory/logs', { params }),\n  getLogById: (id) => api.get(`/factory/logs/${id}`),\n  createLog: (data) => api.post('/factory/logs', data),\n  updateLog: (id, data) => api.put(`/factory/logs/${id}`, data),\n  deleteLog: (id) => api.delete(`/factory/logs/${id}`),\n  getSummary: (params = {}) => api.get('/factory/summary', { params }),\n  getEfficiency: (params = {}) => api.get('/factory/efficiency', { params }),\n};\n\n// Sales API\nexport const salesAPI = {\n  getAll: (params = {}) => api.get('/sales', { params }),\n  getById: (id) => api.get(`/sales/${id}`),\n  create: (data) => api.post('/sales', data),\n  update: (id, data) => api.put(`/sales/${id}`, data),\n  delete: (id) => api.delete(`/sales/${id}`),\n  getDashboard: (params = {}) => api.get('/sales/summary/dashboard', { params }),\n  processRefund: (id, data) => api.post(`/sales/${id}/refund`, data),\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: (params = {}) => api.get('/orders', { params }),\n  getById: (id) => api.get(`/orders/${id}`),\n  getUrgent: () => api.get('/orders/urgent'),\n  create: (data) => api.post('/orders', data),\n  update: (id, data) => api.put(`/orders/${id}`, data),\n  updateStatus: (id, status) => api.patch(`/orders/${id}/status`, { status }),\n  delete: (id) => api.delete(`/orders/${id}`),\n  getDashboard: (params = {}) => api.get('/orders/summary/dashboard', { params }),\n  checkStock: (id) => api.post(`/orders/${id}/check-stock`),\n};\n\n// Webhooks API\nexport const webhooksAPI = {\n  getAll: (params = {}) => api.get('/webhooks', { params }),\n  getById: (id) => api.get(`/webhooks/${id}`),\n  create: (data) => api.post('/webhooks', data),\n  update: (id, data) => api.put(`/webhooks/${id}`, data),\n  delete: (id) => api.delete(`/webhooks/${id}`),\n  toggle: (id) => api.patch(`/webhooks/${id}/toggle`),\n  test: (id) => api.post(`/webhooks/${id}/test`),\n  getStats: (id) => api.get(`/webhooks/${id}/stats`),\n  getEventTypes: () => api.get('/webhooks/events/types'),\n  trigger: (data) => api.post('/webhooks/trigger', data),\n  getRecentLogs: (params = {}) => api.get('/webhooks/logs/recent', { params }),\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => api.get('/health'),\n};\n\nexport default api;\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,aAAa,GAAG;EAC3BC,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,aAAa,EAAE;IAAED;EAAO,CAAC,CAAC;EAC3DE,OAAO,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,eAAeE,EAAE,EAAE,CAAC;EAC7C3B,MAAM,EAAG4B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;EAC/CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,eAAeJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACxDI,MAAM,EAAGL,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,eAAeL,EAAE,EAAE;AAChD,CAAC;;AAED;AACA,OAAO,MAAMM,WAAW,GAAG;EACzBV,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,WAAW,EAAE;IAAED;EAAO,CAAC,CAAC;EACzDE,OAAO,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAC3CO,WAAW,EAAEA,CAAA,KAAMnC,GAAG,CAAC0B,GAAG,CAAC,qBAAqB,CAAC;EACjDzB,MAAM,EAAG4B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,WAAW,EAAED,IAAI,CAAC;EAC7CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACtDO,WAAW,EAAEA,CAACR,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACqC,KAAK,CAAC,aAAaT,EAAE,QAAQ,EAAEC,IAAI,CAAC;EACnEI,MAAM,EAAGL,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,aAAaL,EAAE,EAAE;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMU,UAAU,GAAG;EACxBC,OAAO,EAAEA,CAACd,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,eAAe,EAAE;IAAED;EAAO,CAAC,CAAC;EAC9De,UAAU,EAAGZ,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,iBAAiBE,EAAE,EAAE,CAAC;EAClDa,SAAS,EAAGZ,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,eAAe,EAAED,IAAI,CAAC;EACpDa,SAAS,EAAEA,CAACd,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,iBAAiBJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EAC7Dc,SAAS,EAAGf,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,iBAAiBL,EAAE,EAAE,CAAC;EACpDgB,UAAU,EAAEA,CAACnB,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,kBAAkB,EAAE;IAAED;EAAO,CAAC,CAAC;EACpEoB,aAAa,EAAEA,CAACpB,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,qBAAqB,EAAE;IAAED;EAAO,CAAC;AAC3E,CAAC;;AAED;AACA,OAAO,MAAMqB,QAAQ,GAAG;EACtBtB,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,QAAQ,EAAE;IAAED;EAAO,CAAC,CAAC;EACtDE,OAAO,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;EACxC3B,MAAM,EAAG4B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,QAAQ,EAAED,IAAI,CAAC;EAC1CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,UAAUJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACnDI,MAAM,EAAGL,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,UAAUL,EAAE,EAAE,CAAC;EAC1CmB,YAAY,EAAEA,CAACtB,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,0BAA0B,EAAE;IAAED;EAAO,CAAC,CAAC;EAC9EuB,aAAa,EAAEA,CAACpB,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAAC8B,IAAI,CAAC,UAAUF,EAAE,SAAS,EAAEC,IAAI;AACnE,CAAC;;AAED;AACA,OAAO,MAAMoB,SAAS,GAAG;EACvBzB,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,SAAS,EAAE;IAAED;EAAO,CAAC,CAAC;EACvDE,OAAO,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,WAAWE,EAAE,EAAE,CAAC;EACzCsB,SAAS,EAAEA,CAAA,KAAMlD,GAAG,CAAC0B,GAAG,CAAC,gBAAgB,CAAC;EAC1CzB,MAAM,EAAG4B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,SAAS,EAAED,IAAI,CAAC;EAC3CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,WAAWJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACpDsB,YAAY,EAAEA,CAACvB,EAAE,EAAEV,MAAM,KAAKlB,GAAG,CAACqC,KAAK,CAAC,WAAWT,EAAE,SAAS,EAAE;IAAEV;EAAO,CAAC,CAAC;EAC3Ee,MAAM,EAAGL,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,WAAWL,EAAE,EAAE,CAAC;EAC3CmB,YAAY,EAAEA,CAACtB,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,2BAA2B,EAAE;IAAED;EAAO,CAAC,CAAC;EAC/E2B,UAAU,EAAGxB,EAAE,IAAK5B,GAAG,CAAC8B,IAAI,CAAC,WAAWF,EAAE,cAAc;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMyB,WAAW,GAAG;EACzB7B,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,WAAW,EAAE;IAAED;EAAO,CAAC,CAAC;EACzDE,OAAO,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAC3C3B,MAAM,EAAG4B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,WAAW,EAAED,IAAI,CAAC;EAC7CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAK7B,GAAG,CAACgC,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACtDI,MAAM,EAAGL,EAAE,IAAK5B,GAAG,CAACiC,MAAM,CAAC,aAAaL,EAAE,EAAE,CAAC;EAC7C0B,MAAM,EAAG1B,EAAE,IAAK5B,GAAG,CAACqC,KAAK,CAAC,aAAaT,EAAE,SAAS,CAAC;EACnD2B,IAAI,EAAG3B,EAAE,IAAK5B,GAAG,CAAC8B,IAAI,CAAC,aAAaF,EAAE,OAAO,CAAC;EAC9C4B,QAAQ,EAAG5B,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,aAAaE,EAAE,QAAQ,CAAC;EAClD6B,aAAa,EAAEA,CAAA,KAAMzD,GAAG,CAAC0B,GAAG,CAAC,wBAAwB,CAAC;EACtDgC,OAAO,EAAG7B,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,mBAAmB,EAAED,IAAI,CAAC;EACtD8B,aAAa,EAAEA,CAAClC,MAAM,GAAG,CAAC,CAAC,KAAKzB,GAAG,CAAC0B,GAAG,CAAC,uBAAuB,EAAE;IAAED;EAAO,CAAC;AAC7E,CAAC;;AAED;AACA,OAAO,MAAMmC,SAAS,GAAG;EACvBC,KAAK,EAAEA,CAAA,KAAM7D,GAAG,CAAC0B,GAAG,CAAC,SAAS;AAChC,CAAC;AAED,eAAe1B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}