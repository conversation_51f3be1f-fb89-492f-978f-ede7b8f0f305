// @ts-nocheck
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Categories API
export const categoriesAPI = {
  getAll: (params = {}) => api.get('/categories', { params }),
  getById: (id) => api.get(`/categories/${id}`),
  create: (data) => api.post('/categories', data),
  update: (id, data) => api.put(`/categories/${id}`, data),
  delete: (id) => api.delete(`/categories/${id}`),
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  getLowStock: () => api.get('/products/low-stock'),
  create: (data) => api.post('/products', data),
  update: (id, data) => api.put(`/products/${id}`, data),
  updateStock: (id, data) => api.patch(`/products/${id}/stock`, data),
  delete: (id) => api.delete(`/products/${id}`),
};

// Factory API
export const factoryAPI = {
  getLogs: (params = {}) => api.get('/factory/logs', { params }),
  getLogById: (id) => api.get(`/factory/logs/${id}`),
  createLog: (data) => api.post('/factory/logs', data),
  updateLog: (id, data) => api.put(`/factory/logs/${id}`, data),
  deleteLog: (id) => api.delete(`/factory/logs/${id}`),
  getSummary: (params = {}) => api.get('/factory/summary', { params }),
  getEfficiency: (params = {}) => api.get('/factory/efficiency', { params }),
};

// Sales API
export const salesAPI = {
  getAll: (params = {}) => api.get('/sales', { params }),
  getById: (id) => api.get(`/sales/${id}`),
  create: (data) => api.post('/sales', data),
  update: (id, data) => api.put(`/sales/${id}`, data),
  delete: (id) => api.delete(`/sales/${id}`),
  getDashboard: (params = {}) => api.get('/sales/summary/dashboard', { params }),
  processRefund: (id, data) => api.post(`/sales/${id}/refund`, data),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  getUrgent: () => api.get('/orders/urgent'),
  create: (data) => api.post('/orders', data),
  update: (id, data) => api.put(`/orders/${id}`, data),
  updateStatus: (id, status) => api.patch(`/orders/${id}/status`, { status }),
  delete: (id) => api.delete(`/orders/${id}`),
  getDashboard: (params = {}) => api.get('/orders/summary/dashboard', { params }),
  checkStock: (id) => api.post(`/orders/${id}/check-stock`),
};

// Webhooks API
export const webhooksAPI = {
  getAll: (params = {}) => api.get('/webhooks', { params }),
  getById: (id) => api.get(`/webhooks/${id}`),
  create: (data) => api.post('/webhooks', data),
  update: (id, data) => api.put(`/webhooks/${id}`, data),
  delete: (id) => api.delete(`/webhooks/${id}`),
  toggle: (id) => api.patch(`/webhooks/${id}/toggle`),
  test: (id) => api.post(`/webhooks/${id}/test`),
  getStats: (id) => api.get(`/webhooks/${id}/stats`),
  getEventTypes: () => api.get('/webhooks/events/types'),
  trigger: (data) => api.post('/webhooks/trigger', data),
  getRecentLogs: (params = {}) => api.get('/webhooks/logs/recent', { params }),
};

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
