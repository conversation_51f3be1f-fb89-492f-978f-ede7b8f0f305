const { executeQuery, connectDB } = require('./src/config/database');

async function checkTables() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    console.log('🔍 Checking database tables...');
    
    // Show all tables
    const tables = await executeQuery('SHOW TABLES');
    console.log('\n📋 Available tables:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`  - ${tableName}`);
    });
    
    // Check products table structure
    console.log('\n🔍 Products table structure:');
    const productsStructure = await executeQuery('DESCRIBE products');
    console.table(productsStructure);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkTables();
