const { executeQuery } = require('../config/database');

class Product {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.sku = data.sku;
    this.barcode = data.barcode;
    this.categoryId = data.category_id || data.categoryId;
    this.category = data.category; // For populated data
    this.price = data.price;
    this.cost = data.cost || data.costPrice;
    this.currentStock = data.current_stock || data.currentStock;
    this.minStockLevel = data.min_stock_level || data.minStockLevel;
    this.maxStockLevel = data.max_stock_level || data.maxStockLevel;
    this.isFinishedProduct = data.is_finished_product !== undefined ? data.is_finished_product : data.isFinishedProduct;
    this.isActive = data.is_active !== undefined ? data.is_active : data.isActive;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // Convert database row to Product instance
  static fromRow(row) {
    const product = new Product({
      id: row.id,
      name: row.name,
      description: row.description,
      sku: row.sku,
      barcode: row.barcode,
      category_id: row.category_id,
      price: row.price,
      cost: row.cost,
      current_stock: row.current_stock,
      min_stock_level: row.min_stock_level,
      max_stock_level: row.max_stock_level,
      is_finished_product: row.is_finished_product,
      is_active: row.is_active,
      created_at: row.created_at,
      updated_at: row.updated_at
    });

    // Add category data if joined
    if (row.category_name) {
      product.category = {
        _id: row.category_id,
        id: row.category_id,
        name: row.category_name,
        unitType: row.category_unit_type,
        unitLabel: row.category_unit_label
      };
    }

    return product;
  }

  // Virtual for stock status
  get stockStatus() {
    if (this.currentStock === 0) return 'out';
    if (this.currentStock <= this.minStockLevel) return 'low';
    return 'normal';
  }

  // Convert to API response format (Mongoose compatibility)
  toJSON() {
    return {
      _id: this.id,
      id: this.id,
      name: this.name,
      description: this.description,
      sku: this.sku,
      barcode: this.barcode,
      category: this.category || this.categoryId,
      price: this.price,
      cost: this.cost,
      currentStock: this.currentStock,
      minStockLevel: this.minStockLevel,
      maxStockLevel: this.maxStockLevel,
      stockStatus: this.stockStatus,
      isFinishedProduct: this.isFinishedProduct,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // Find all products with query builder
  static find(conditions = {}) {
    const queryBuilder = {
      conditions,
      sortField: null,
      sortOrder: 'ASC',
      limitValue: null,
      skipValue: 0,
      populateCategory: false,

      sort: function(sortOptions) {
        if (typeof sortOptions === 'string') {
          this.sortField = sortOptions.replace('-', '');
          this.sortOrder = sortOptions.startsWith('-') ? 'DESC' : 'ASC';
        } else if (typeof sortOptions === 'object') {
          const field = Object.keys(sortOptions)[0];
          this.sortField = field;
          this.sortOrder = sortOptions[field] === -1 ? 'DESC' : 'ASC';
        }
        return this;
      },

      limit: function(value) {
        this.limitValue = parseInt(value);
        return this;
      },

      skip: function(value) {
        this.skipValue = parseInt(value);
        return this;
      },

      populate: function(field) {
        if (field === 'category') {
          this.populateCategory = true;
        }
        return this;
      },

      exec: async function() {
        try {
          let query = `
            SELECT p.*,
                   c.name as category_name,
                   c.unit_type as category_unit_type,
                   c.unit_label as category_unit_label
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE 1=1
          `;
          const params = [];

          if (this.conditions.isActive !== undefined) {
            query += ' AND p.is_active = ?';
            params.push(this.conditions.isActive ? 1 : 0);
          }

          if (this.conditions.category) {
            query += ' AND p.category_id = ?';
            params.push(this.conditions.category);
          }

          if (this.conditions.isFinishedProduct !== undefined) {
            query += ' AND p.is_finished_product = ?';
            params.push(this.conditions.isFinishedProduct ? 1 : 0);
          }

          if (this.sortField) {
            const field = this.sortField === 'name' ? 'p.name' : `p.${this.sortField}`;
            query += ` ORDER BY ${field} ${this.sortOrder}`;
          } else {
            query += ' ORDER BY p.name ASC';
          }

          if (this.limitValue) {
            query += ` LIMIT ${this.limitValue}`;

            if (this.skipValue > 0) {
              query += ` OFFSET ${this.skipValue}`;
            }
          }

          const rows = await executeQuery(query, params);
          return rows.map(row => Product.fromRow(row));
        } catch (error) {
          console.error('Error finding products:', error);
          return [];
        }
      }
    };

    // For direct await usage
    queryBuilder.then = function(resolve, reject) {
      return this.exec().then(resolve, reject);
    };

    return queryBuilder;
  }

  // Count products
  static async countDocuments(conditions = {}) {
    try {
      let query = 'SELECT COUNT(*) as count FROM products WHERE 1=1';
      const params = [];

      if (conditions.isActive !== undefined) {
        query += ' AND is_active = ?';
        params.push(conditions.isActive ? 1 : 0);
      }

      if (conditions.category) {
        query += ' AND category_id = ?';
        params.push(conditions.category);
      }

      if (conditions.isFinishedProduct !== undefined) {
        query += ' AND is_finished_product = ?';
        params.push(conditions.isFinishedProduct ? 1 : 0);
      }

      const rows = await executeQuery(query, params);
      return rows[0].count;
    } catch (error) {
      console.error('Error counting products:', error);
      return 0;
    }
  }
}

module.exports = Product;
