{"name": "jussamy", "version": "1.0.0", "description": "Dynamic web app for managing stock, factory production, sales, and orders", "main": "backend/src/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && nodemon src/server.js", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["inventory", "production", "sales", "orders", "api", "webhook"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "socket.io": "^4.8.1"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}