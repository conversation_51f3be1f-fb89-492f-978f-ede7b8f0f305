/**
 * Unity-specific webhook configurations and payload generators
 */

const webhookService = require('../services/webhookService');

/**
 * Unity webhook event types specifically designed for mobile app integration
 */
const UNITY_WEBHOOK_EVENTS = {
  // Stock-related events for inventory alerts
  STOCK_LOW: 'unity.stock.low',
  STOCK_CRITICAL: 'unity.stock.critical',
  STOCK_UPDATED: 'unity.stock.updated',
  
  // Order-related events for order management
  ORDER_CREATED: 'unity.order.created',
  ORDER_URGENT: 'unity.order.urgent',
  ORDER_READY: 'unity.order.ready',
  
  // Sales events for real-time updates
  SALE_COMPLETED: 'unity.sale.completed',
  DAILY_SALES_SUMMARY: 'unity.sales.daily_summary',
  
  // Production events
  PRODUCTION_COMPLETED: 'unity.production.completed',
  PRODUCTION_ALERT: 'unity.production.alert',
  
  // System events
  SYNC_REQUEST: 'unity.sync.request',
  ALERT_NOTIFICATION: 'unity.alert.notification'
};

/**
 * Generate Unity-optimized stock low alert payload
 */
const generateUnityStockLowPayload = (products) => ({
  event: UNITY_WEBHOOK_EVENTS.STOCK_LOW,
  timestamp: new Date().toISOString(),
  data: {
    alertType: 'stock_low',
    severity: products.length > 10 ? 'critical' : 'warning',
    products: products.map(product => ({
      id: product._id,
      name: product.name,
      currentStock: product.currentStock,
      minStockLevel: product.minStockLevel,
      shortage: product.minStockLevel - product.currentStock,
      category: product.category?.name,
      unitLabel: product.category?.unitLabel
    })),
    totalAffectedProducts: products.length,
    actionRequired: true,
    suggestedActions: [
      'Review supplier orders',
      'Check production schedule',
      'Update minimum stock levels'
    ]
  },
  unity: {
    notificationType: 'alert',
    priority: products.length > 10 ? 'high' : 'medium',
    displayDuration: 10000, // 10 seconds
    soundAlert: true
  }
});

/**
 * Generate Unity-optimized order alert payload
 */
const generateUnityOrderAlertPayload = (order) => ({
  event: UNITY_WEBHOOK_EVENTS.ORDER_URGENT,
  timestamp: new Date().toISOString(),
  data: {
    alertType: 'order_urgent',
    order: {
      id: order._id,
      orderNumber: order.orderNumber,
      customerName: order.customer.name,
      customerPhone: order.customer.phone,
      deliveryDate: order.deliveryDate,
      totalAmount: order.totalAmount,
      status: order.status,
      daysUntilDelivery: order.daysUntilDelivery,
      urgencyLevel: order.urgencyLevel
    },
    stockAlert: order.stockAlert,
    actionRequired: true
  },
  unity: {
    notificationType: 'urgent_order',
    priority: 'high',
    displayDuration: 15000, // 15 seconds
    soundAlert: true,
    vibration: true
  }
});

/**
 * Generate Unity-optimized sales summary payload
 */
const generateUnitySalesSummaryPayload = (salesData) => ({
  event: UNITY_WEBHOOK_EVENTS.DAILY_SALES_SUMMARY,
  timestamp: new Date().toISOString(),
  data: {
    date: new Date().toISOString().split('T')[0],
    summary: {
      totalSales: salesData.totalSales,
      totalRevenue: salesData.totalRevenue,
      avgSaleValue: salesData.avgSaleValue,
      topProducts: salesData.topProducts?.slice(0, 5) || []
    },
    performance: {
      vsYesterday: salesData.vsYesterday || 0,
      vsLastWeek: salesData.vsLastWeek || 0,
      trend: salesData.trend || 'stable'
    }
  },
  unity: {
    notificationType: 'info',
    priority: 'low',
    displayDuration: 5000,
    soundAlert: false
  }
});

/**
 * Generate Unity-optimized production completion payload
 */
const generateUnityProductionPayload = (productionLog) => ({
  event: UNITY_WEBHOOK_EVENTS.PRODUCTION_COMPLETED,
  timestamp: new Date().toISOString(),
  data: {
    production: {
      id: productionLog._id,
      batchNumber: productionLog.batchNumber,
      operator: productionLog.operator,
      shift: productionLog.shift,
      efficiency: productionLog.efficiency,
      totalCost: productionLog.totalCost,
      totalValue: productionLog.totalValue,
      profitLoss: productionLog.profitLoss
    },
    materialsUsed: productionLog.materialsUsed?.map(material => ({
      productName: material.product?.name,
      quantity: material.quantity,
      unit: material.unit,
      cost: material.cost
    })) || [],
    productsGenerated: productionLog.productsGenerated?.map(product => ({
      productName: product.product?.name,
      quantity: product.quantity,
      unit: product.unit,
      estimatedValue: product.estimatedValue
    })) || []
  },
  unity: {
    notificationType: 'success',
    priority: 'medium',
    displayDuration: 8000,
    soundAlert: false
  }
});

/**
 * Generate Unity sync request payload
 */
const generateUnitySyncPayload = (syncType = 'full') => ({
  event: UNITY_WEBHOOK_EVENTS.SYNC_REQUEST,
  timestamp: new Date().toISOString(),
  data: {
    syncType, // 'full', 'incremental', 'products_only', 'orders_only'
    lastSyncTime: new Date().toISOString(),
    endpoints: {
      products: '/api/unity/products',
      orders: '/api/unity/orders',
      dashboard: '/api/unity/dashboard',
      lowStock: '/api/unity/products/low-stock'
    },
    authRequired: true,
    apiKey: 'required'
  },
  unity: {
    notificationType: 'sync',
    priority: 'low',
    displayDuration: 3000,
    soundAlert: false
  }
});

/**
 * Trigger Unity-specific webhooks
 */
class UnityWebhookService {
  /**
   * Trigger stock low alert for Unity app
   */
  static async triggerStockLowAlert(products) {
    const payload = generateUnityStockLowPayload(products);
    await webhookService.triggerWebhook(UNITY_WEBHOOK_EVENTS.STOCK_LOW, payload.data);
  }

  /**
   * Trigger urgent order alert for Unity app
   */
  static async triggerOrderAlert(order) {
    const payload = generateUnityOrderAlertPayload(order);
    await webhookService.triggerWebhook(UNITY_WEBHOOK_EVENTS.ORDER_URGENT, payload.data);
  }

  /**
   * Trigger daily sales summary for Unity app
   */
  static async triggerSalesSummary(salesData) {
    const payload = generateUnitySalesSummaryPayload(salesData);
    await webhookService.triggerWebhook(UNITY_WEBHOOK_EVENTS.DAILY_SALES_SUMMARY, payload.data);
  }

  /**
   * Trigger production completion notification for Unity app
   */
  static async triggerProductionComplete(productionLog) {
    const payload = generateUnityProductionPayload(productionLog);
    await webhookService.triggerWebhook(UNITY_WEBHOOK_EVENTS.PRODUCTION_COMPLETED, payload.data);
  }

  /**
   * Trigger sync request for Unity app
   */
  static async triggerSyncRequest(syncType = 'full') {
    const payload = generateUnitySyncPayload(syncType);
    await webhookService.triggerWebhook(UNITY_WEBHOOK_EVENTS.SYNC_REQUEST, payload.data);
  }

  /**
   * Get all Unity webhook event types
   */
  static getEventTypes() {
    return Object.values(UNITY_WEBHOOK_EVENTS).map(eventType => ({
      type: eventType,
      description: this.getEventDescription(eventType),
      unityOptimized: true
    }));
  }

  /**
   * Get description for Unity webhook event type
   */
  static getEventDescription(eventType) {
    const descriptions = {
      [UNITY_WEBHOOK_EVENTS.STOCK_LOW]: 'Triggered when products fall below minimum stock levels - optimized for Unity mobile alerts',
      [UNITY_WEBHOOK_EVENTS.STOCK_CRITICAL]: 'Triggered when products are critically low or out of stock',
      [UNITY_WEBHOOK_EVENTS.STOCK_UPDATED]: 'Triggered when stock levels are updated - for real-time Unity sync',
      [UNITY_WEBHOOK_EVENTS.ORDER_CREATED]: 'Triggered when new orders are created from Unity app',
      [UNITY_WEBHOOK_EVENTS.ORDER_URGENT]: 'Triggered for urgent orders requiring immediate attention',
      [UNITY_WEBHOOK_EVENTS.ORDER_READY]: 'Triggered when orders are ready for delivery',
      [UNITY_WEBHOOK_EVENTS.SALE_COMPLETED]: 'Triggered when sales are completed through Unity app',
      [UNITY_WEBHOOK_EVENTS.DAILY_SALES_SUMMARY]: 'Daily sales summary for Unity dashboard updates',
      [UNITY_WEBHOOK_EVENTS.PRODUCTION_COMPLETED]: 'Triggered when production batches are completed',
      [UNITY_WEBHOOK_EVENTS.PRODUCTION_ALERT]: 'Production-related alerts and notifications',
      [UNITY_WEBHOOK_EVENTS.SYNC_REQUEST]: 'Sync request for Unity app data synchronization',
      [UNITY_WEBHOOK_EVENTS.ALERT_NOTIFICATION]: 'General alert notifications for Unity app'
    };
    return descriptions[eventType] || 'Unity-optimized webhook event';
  }
}

module.exports = {
  UNITY_WEBHOOK_EVENTS,
  UnityWebhookService,
  generateUnityStockLowPayload,
  generateUnityOrderAlertPayload,
  generateUnitySalesSummaryPayload,
  generateUnityProductionPayload,
  generateUnitySyncPayload
};
