const { executeQuery } = require('../../config/database');

class Category {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.unitType = data.unit_type || data.unitType;
    this.unitLabel = data.unit_label || data.unitLabel;
    this.isActive = data.is_active !== undefined ? data.is_active : data.isActive;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // Convert database row to Category instance
  static fromRow(row) {
    return new Category({
      id: row.id,
      name: row.name,
      description: row.description,
      unit_type: row.unit_type,
      unit_label: row.unit_label,
      is_active: row.is_active,
      created_at: row.created_at,
      updated_at: row.updated_at
    });
  }

  // Convert Category instance to database format
  toDatabase() {
    return {
      name: this.name,
      description: this.description,
      unit_type: this.unitType,
      unit_label: this.unitLabel,
      is_active: this.isActive
    };
  }

  // Convert to API response format
  toJSON() {
    return {
      _id: this.id,
      id: this.id,
      name: this.name,
      description: this.description,
      unitType: this.unitType,
      unitLabel: this.unitLabel,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // Find all categories
  static async find(conditions = {}) {
    try {
      let query = 'SELECT * FROM categories WHERE 1=1';
      const params = [];

      if (conditions.isActive !== undefined) {
        query += ' AND is_active = ?';
        params.push(conditions.isActive);
      }

      if (conditions.name) {
        query += ' AND name LIKE ?';
        params.push(`%${conditions.name}%`);
      }

      query += ' ORDER BY name ASC';

      const rows = await executeQuery(query, params);
      return rows.map(row => Category.fromRow(row));
    } catch (error) {
      throw new Error(`Error finding categories: ${error.message}`);
    }
  }

  // Find category by ID
  static async findById(id) {
    try {
      const rows = await executeQuery('SELECT * FROM categories WHERE id = ?', [id]);
      return rows.length > 0 ? Category.fromRow(rows[0]) : null;
    } catch (error) {
      throw new Error(`Error finding category by ID: ${error.message}`);
    }
  }

  // Find category by name
  static async findByName(name) {
    try {
      const rows = await executeQuery('SELECT * FROM categories WHERE name = ?', [name]);
      return rows.length > 0 ? Category.fromRow(rows[0]) : null;
    } catch (error) {
      throw new Error(`Error finding category by name: ${error.message}`);
    }
  }

  // Create new category
  static async create(categoryData) {
    try {
      const category = new Category(categoryData);
      const data = category.toDatabase();
      
      const result = await executeQuery(
        'INSERT INTO categories (name, description, unit_type, unit_label, is_active) VALUES (?, ?, ?, ?, ?)',
        [data.name, data.description, data.unit_type, data.unit_label, data.is_active]
      );

      category.id = result.insertId;
      return category;
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Category name already exists');
      }
      throw new Error(`Error creating category: ${error.message}`);
    }
  }

  // Update category
  async save() {
    try {
      if (!this.id) {
        throw new Error('Cannot update category without ID');
      }

      const data = this.toDatabase();
      await executeQuery(
        'UPDATE categories SET name = ?, description = ?, unit_type = ?, unit_label = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [data.name, data.description, data.unit_type, data.unit_label, data.is_active, this.id]
      );

      return this;
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Category name already exists');
      }
      throw new Error(`Error updating category: ${error.message}`);
    }
  }

  // Delete category
  static async findByIdAndDelete(id) {
    try {
      // Check if category is used by products
      const productRows = await executeQuery('SELECT COUNT(*) as count FROM products WHERE category_id = ?', [id]);
      if (productRows[0].count > 0) {
        throw new Error('Cannot delete category that is used by products');
      }

      const category = await Category.findById(id);
      if (!category) {
        return null;
      }

      await executeQuery('DELETE FROM categories WHERE id = ?', [id]);
      return category;
    } catch (error) {
      throw new Error(`Error deleting category: ${error.message}`);
    }
  }

  // Count categories
  static async countDocuments(conditions = {}) {
    try {
      let query = 'SELECT COUNT(*) as count FROM categories WHERE 1=1';
      const params = [];

      if (conditions.isActive !== undefined) {
        query += ' AND is_active = ?';
        params.push(conditions.isActive);
      }

      const rows = await executeQuery(query, params);
      return rows[0].count;
    } catch (error) {
      throw new Error(`Error counting categories: ${error.message}`);
    }
  }

  // Get categories with product counts
  static async findWithProductCounts() {
    try {
      const query = `
        SELECT 
          c.*,
          COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        WHERE c.is_active = 1
        GROUP BY c.id
        ORDER BY c.name ASC
      `;

      const rows = await executeQuery(query);
      return rows.map(row => ({
        ...Category.fromRow(row).toJSON(),
        productCount: row.product_count
      }));
    } catch (error) {
      throw new Error(`Error finding categories with product counts: ${error.message}`);
    }
  }

  // Validate category data
  static validate(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Category name is required');
    }

    if (data.name && data.name.length > 255) {
      errors.push('Category name must be less than 255 characters');
    }

    if (data.unitType && !['unit', 'weight', 'volume', 'length'].includes(data.unitType)) {
      errors.push('Unit type must be one of: unit, weight, volume, length');
    }

    if (data.unitLabel && data.unitLabel.length > 50) {
      errors.push('Unit label must be less than 50 characters');
    }

    return errors;
  }
}

module.exports = Category;
