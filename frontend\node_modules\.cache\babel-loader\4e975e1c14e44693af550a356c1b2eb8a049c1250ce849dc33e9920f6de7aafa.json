{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\JusSamy\\\\frontend\\\\src\\\\components\\\\SalesTab.tsx\",\n  _s = $RefreshSig$();\n// @ts-nocheck\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, Button, Grid, Card, CardContent, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Accordion, AccordionSummary, AccordionDetails, IconButton } from '@mui/material';\nimport { Add as AddIcon, PointOfSale as SalesIcon, TrendingUp as TrendingUpIcon, AttachMoney as MoneyIcon, Receipt as ReceiptIcon, ExpandMore as ExpandMoreIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';\nimport { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { salesAPI, productsAPI } from '../services/api';\nimport { format } from 'date-fns';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SalesTab = () => {\n  _s();\n  var _dashboard$summary, _dashboard$summary2, _dashboard$summary2$t, _dashboard$summary3, _dashboard$summary3$a, _dashboard$summary4, _dashboard$summary4$t;\n  const [sales, setSales] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [dashboard, setDashboard] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n\n  // Form state for sale creation\n  const [formData, setFormData] = useState({\n    customer: {\n      name: '',\n      email: '',\n      phone: '',\n      address: ''\n    },\n    items: [{\n      product: '',\n      quantity: 1,\n      unitPrice: 0\n    }],\n    paymentMethod: 'cash',\n    salesPerson: '',\n    notes: '',\n    tax: {\n      rate: 0,\n      amount: 0\n    },\n    discount: 0\n  });\n  useEffect(() => {\n    fetchSales();\n    fetchProducts();\n    fetchDashboard();\n  }, []);\n  const fetchSales = async () => {\n    try {\n      setLoading(true);\n      const response = await salesAPI.getAll();\n      setSales(response.data.sales);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to fetch sales');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchProducts = async () => {\n    try {\n      const response = await productsAPI.getAll();\n      setProducts(response.data.products);\n    } catch (err) {\n      console.error('Failed to fetch products:', err);\n    }\n  };\n  const fetchDashboard = async () => {\n    try {\n      const response = await salesAPI.getDashboard();\n      setDashboard(response.data);\n    } catch (err) {\n      console.error('Failed to fetch dashboard:', err);\n    }\n  };\n  const handleCreateSale = async () => {\n    try {\n      await salesAPI.create(formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchSales();\n      fetchDashboard();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to create sale');\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      customer: {\n        name: '',\n        email: '',\n        phone: '',\n        address: ''\n      },\n      items: [{\n        product: '',\n        quantity: 1,\n        unitPrice: 0\n      }],\n      paymentMethod: 'cash',\n      salesPerson: '',\n      notes: '',\n      tax: {\n        rate: 0,\n        amount: 0\n      },\n      discount: 0\n    });\n  };\n  const addItemRow = () => {\n    setFormData({\n      ...formData,\n      items: [...formData.items, {\n        product: '',\n        quantity: 1,\n        unitPrice: 0\n      }]\n    });\n  };\n  const removeItemRow = index => {\n    const updated = formData.items.filter((_, i) => i !== index);\n    setFormData({\n      ...formData,\n      items: updated\n    });\n  };\n  const updateItem = (index, field, value) => {\n    const updated = [...formData.items];\n    updated[index] = {\n      ...updated[index],\n      [field]: value\n    };\n\n    // Auto-fill price when product is selected\n    if (field === 'product') {\n      const selectedProduct = products.find(p => p._id === value);\n      if (selectedProduct) {\n        updated[index].unitPrice = selectedProduct.price;\n      }\n    }\n    setFormData({\n      ...formData,\n      items: updated\n    });\n  };\n  const getPaymentStatusColor = status => {\n    switch (status) {\n      case 'paid':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'partial':\n        return 'info';\n      case 'refunded':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const columns = [{\n    field: 'saleNumber',\n    headerName: 'Sale Number',\n    width: 150\n  }, {\n    field: 'date',\n    headerName: 'Date',\n    width: 120,\n    valueFormatter: params => format(new Date(params.value), 'MMM dd, yyyy')\n  }, {\n    field: 'customer',\n    headerName: 'Customer',\n    width: 200,\n    valueGetter: params => params.row.customer.name || 'Walk-in Customer'\n  }, {\n    field: 'totalAmount',\n    headerName: 'Total Amount',\n    width: 120,\n    valueFormatter: params => `$${params.value.toFixed(2)}`\n  }, {\n    field: 'paymentMethod',\n    headerName: 'Payment Method',\n    width: 130\n  }, {\n    field: 'paymentStatus',\n    headerName: 'Status',\n    width: 120,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Chip, {\n      label: params.value,\n      color: getPaymentStatusColor(params.value),\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'salesPerson',\n    headerName: 'Sales Person',\n    width: 150\n  }, {\n    field: 'actions',\n    type: 'actions',\n    headerName: 'Actions',\n    width: 100,\n    getActions: params => [/*#__PURE__*/_jsxDEV(GridActionsCellItem, {\n      icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 17\n      }, this),\n      label: \"View Receipt\",\n      onClick: () => console.log('View receipt for', params.row._id)\n    }, \"receipt\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this)]\n  }];\n\n  // Chart colors\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];\n  const calculateSubtotal = () => {\n    return formData.items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);\n  };\n  const calculateTotal = () => {\n    const subtotal = calculateSubtotal();\n    const taxAmount = subtotal * formData.tax.rate / 100;\n    return subtotal + taxAmount - formData.discount;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Sales Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(SalesIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: (dashboard === null || dashboard === void 0 ? void 0 : (_dashboard$summary = dashboard.summary) === null || _dashboard$summary === void 0 ? void 0 : _dashboard$summary.totalSales) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Total Sales\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(MoneyIcon, {\n                color: \"success\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [\"$\", (dashboard === null || dashboard === void 0 ? void 0 : (_dashboard$summary2 = dashboard.summary) === null || _dashboard$summary2 === void 0 ? void 0 : (_dashboard$summary2$t = _dashboard$summary2.totalRevenue) === null || _dashboard$summary2$t === void 0 ? void 0 : _dashboard$summary2$t.toFixed(2)) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Total Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                color: \"info\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [\"$\", (dashboard === null || dashboard === void 0 ? void 0 : (_dashboard$summary3 = dashboard.summary) === null || _dashboard$summary3 === void 0 ? void 0 : (_dashboard$summary3$a = _dashboard$summary3.avgSaleValue) === null || _dashboard$summary3$a === void 0 ? void 0 : _dashboard$summary3$a.toFixed(2)) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Average Sale Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n                color: \"warning\",\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [\"$\", (dashboard === null || dashboard === void 0 ? void 0 : (_dashboard$summary4 = dashboard.summary) === null || _dashboard$summary4 === void 0 ? void 0 : (_dashboard$summary4$t = _dashboard$summary4.totalTax) === null || _dashboard$summary4$t === void 0 ? void 0 : _dashboard$summary4$t.toFixed(2)) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Total Tax Collected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Daily Sales Trend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: (dashboard === null || dashboard === void 0 ? void 0 : dashboard.dailyTrend) || [],\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"dailyRevenue\",\n                stroke: \"#8884d8\",\n                name: \"Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"dailySales\",\n                stroke: \"#82ca9d\",\n                name: \"Sales Count\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Payment Methods\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: (dashboard === null || dashboard === void 0 ? void 0 : dashboard.paymentMethodBreakdown) || [],\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: ({\n                  _id,\n                  count\n                }) => `${_id}: ${count}`,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"count\",\n                children: ((dashboard === null || dashboard === void 0 ? void 0 : dashboard.paymentMethodBreakdown) || []).map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Top Selling Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: ((dashboard === null || dashboard === void 0 ? void 0 : dashboard.topProducts) || []).slice(0, 5).map((product, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2.4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: product.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                children: [\"$\", product.totalRevenue.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: [product.totalQuantity, \" units sold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 22\n        }, this),\n        onClick: () => setOpenDialog(true),\n        children: \"Record New Sale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        height: 600,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataGrid, {\n        rows: sales,\n        columns: columns,\n        getRowId: row => row._id,\n        loading: loading,\n        pageSizeOptions: [25, 50, 100],\n        initialState: {\n          pagination: {\n            paginationModel: {\n              page: 0,\n              pageSize: 25\n            }\n          }\n        },\n        disableRowSelectionOnClick: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Record New Sale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Customer Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Customer Name\",\n              value: formData.customer.name,\n              onChange: e => setFormData({\n                ...formData,\n                customer: {\n                  ...formData.customer,\n                  name: e.target.value\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email\",\n              type: \"email\",\n              value: formData.customer.email,\n              onChange: e => setFormData({\n                ...formData,\n                customer: {\n                  ...formData.customer,\n                  email: e.target.value\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Phone\",\n              value: formData.customer.phone,\n              onChange: e => setFormData({\n                ...formData,\n                customer: {\n                  ...formData.customer,\n                  phone: e.target.value\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Sales Person\",\n              value: formData.salesPerson,\n              onChange: e => setFormData({\n                ...formData,\n                salesPerson: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 43\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Sale Items\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: [formData.items.map((item, index) => /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              sx: {\n                mb: 2\n              },\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  size: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: item.product,\n                    label: \"Product\",\n                    onChange: e => updateItem(index, 'product', e.target.value),\n                    children: products.map(product => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: product._id,\n                      children: [product.name, \" (Stock: \", product.currentStock, \")\"]\n                    }, product._id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 2,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  size: \"small\",\n                  label: \"Quantity\",\n                  type: \"number\",\n                  value: item.quantity,\n                  onChange: e => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 3,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  size: \"small\",\n                  label: \"Unit Price\",\n                  type: \"number\",\n                  value: item.unitPrice,\n                  onChange: e => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 2,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"$\", (item.quantity * item.unitPrice).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 1,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => removeItemRow(index),\n                  disabled: formData.items.length === 1,\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: addItemRow,\n              size: \"small\",\n              children: \"Add Item\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Payment Method\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.paymentMethod,\n                label: \"Payment Method\",\n                onChange: e => setFormData({\n                  ...formData,\n                  paymentMethod: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"cash\",\n                  children: \"Cash\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"card\",\n                  children: \"Card\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"bank_transfer\",\n                  children: \"Bank Transfer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"check\",\n                  children: \"Check\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"credit\",\n                  children: \"Credit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tax Rate (%)\",\n              type: \"number\",\n              value: formData.tax.rate,\n              onChange: e => setFormData({\n                ...formData,\n                tax: {\n                  ...formData.tax,\n                  rate: parseFloat(e.target.value) || 0\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Discount\",\n              type: \"number\",\n              value: formData.discount,\n              onChange: e => setFormData({\n                ...formData,\n                discount: parseFloat(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Total Amount\",\n              value: `$${calculateTotal().toFixed(2)}`,\n              InputProps: {\n                readOnly: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenDialog(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateSale,\n          variant: \"contained\",\n          children: \"Record Sale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(SalesTab, \"3P6CxOKrv4hI880kpoYXTB7BO7o=\");\n_c = SalesTab;\nexport default SalesTab;\nvar _c;\n$RefreshReg$(_c, \"SalesTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "IconButton", "Add", "AddIcon", "PointOfSale", "SalesIcon", "TrendingUp", "TrendingUpIcon", "AttachMoney", "MoneyIcon", "Receipt", "ReceiptIcon", "ExpandMore", "ExpandMoreIcon", "Delete", "DeleteIcon", "DataGrid", "GridActionsCellItem", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "salesAPI", "productsAPI", "format", "jsxDEV", "_jsxDEV", "SalesTab", "_s", "_dashboard$summary", "_dashboard$summary2", "_dashboard$summary2$t", "_dashboard$summary3", "_dashboard$summary3$a", "_dashboard$summary4", "_dashboard$summary4$t", "sales", "setSales", "products", "setProducts", "dashboard", "setDashboard", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "formData", "setFormData", "customer", "name", "email", "phone", "address", "items", "product", "quantity", "unitPrice", "paymentMethod", "salesPerson", "notes", "tax", "rate", "amount", "discount", "fetchSales", "fetchProducts", "fetchDashboard", "response", "getAll", "data", "err", "_err$response", "_err$response$data", "message", "console", "getDashboard", "handleCreateSale", "create", "resetForm", "_err$response2", "_err$response2$data", "addItemRow", "removeItemRow", "index", "updated", "filter", "_", "i", "updateItem", "field", "value", "selectedProduct", "find", "p", "_id", "price", "getPaymentStatusColor", "status", "columns", "headerName", "width", "valueFormatter", "params", "Date", "valueGetter", "row", "toFixed", "renderCell", "label", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "getActions", "icon", "onClick", "log", "COLORS", "calculateSubtotal", "reduce", "sum", "item", "calculateTotal", "subtotal", "taxAmount", "children", "variant", "gutterBottom", "container", "spacing", "sx", "mb", "xs", "sm", "md", "display", "alignItems", "mr", "summary", "totalSales", "totalRevenue", "avgSaleValue", "totalTax", "height", "dailyTrend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "paymentMethodBreakdown", "cx", "cy", "labelLine", "count", "outerRadius", "fill", "map", "entry", "length", "topProducts", "slice", "productName", "totalQuantity", "startIcon", "severity", "onClose", "rows", "getRowId", "pageSizeOptions", "initialState", "pagination", "paginationModel", "page", "pageSize", "disableRowSelectionOnClick", "open", "max<PERSON><PERSON><PERSON>", "fullWidth", "mt", "onChange", "e", "target", "expandIcon", "currentStock", "parseFloat", "disabled", "InputProps", "readOnly", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/JusSamy/frontend/src/components/SalesTab.tsx"], "sourcesContent": ["// @ts-nocheck\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  IconButton\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  PointOfSale as SalesIcon,\n  TrendingUp as TrendingUpIcon,\n  AttachMoney as MoneyIcon,\n  Receipt as ReceiptIcon,\n  ExpandMore as ExpandMoreIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { salesAPI, productsAPI } from '../services/api';\nimport { format } from 'date-fns';\n\ninterface Sale {\n  _id: string;\n  saleNumber: string;\n  date: string;\n  customer: {\n    name: string;\n    email: string;\n    phone: string;\n  };\n  items: Array<{\n    product: { _id: string; name: string };\n    quantity: number;\n    unitPrice: number;\n    finalPrice: number;\n  }>;\n  totalAmount: number;\n  paymentMethod: string;\n  paymentStatus: string;\n  salesPerson: string;\n}\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  currentStock: number;\n}\n\nconst SalesTab: React.FC = () => {\n  const [sales, setSales] = useState<Sale[]>([]);\n  const [products, setProducts] = useState<Product[]>([]);\n  const [dashboard, setDashboard] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [openDialog, setOpenDialog] = useState(false);\n\n  // Form state for sale creation\n  const [formData, setFormData] = useState({\n    customer: {\n      name: '',\n      email: '',\n      phone: '',\n      address: ''\n    },\n    items: [{ product: '', quantity: 1, unitPrice: 0 }],\n    paymentMethod: 'cash' as 'cash' | 'card' | 'bank_transfer' | 'check' | 'credit',\n    salesPerson: '',\n    notes: '',\n    tax: { rate: 0, amount: 0 },\n    discount: 0\n  });\n\n  useEffect(() => {\n    fetchSales();\n    fetchProducts();\n    fetchDashboard();\n  }, []);\n\n  const fetchSales = async () => {\n    try {\n      setLoading(true);\n      const response = await salesAPI.getAll();\n      setSales(response.data.sales);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to fetch sales');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchProducts = async () => {\n    try {\n      const response = await productsAPI.getAll();\n      setProducts(response.data.products);\n    } catch (err: any) {\n      console.error('Failed to fetch products:', err);\n    }\n  };\n\n  const fetchDashboard = async () => {\n    try {\n      const response = await salesAPI.getDashboard();\n      setDashboard(response.data);\n    } catch (err: any) {\n      console.error('Failed to fetch dashboard:', err);\n    }\n  };\n\n  const handleCreateSale = async () => {\n    try {\n      await salesAPI.create(formData);\n      setOpenDialog(false);\n      resetForm();\n      fetchSales();\n      fetchDashboard();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create sale');\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      customer: { name: '', email: '', phone: '', address: '' },\n      items: [{ product: '', quantity: 1, unitPrice: 0 }],\n      paymentMethod: 'cash',\n      salesPerson: '',\n      notes: '',\n      tax: { rate: 0, amount: 0 },\n      discount: 0\n    });\n  };\n\n  const addItemRow = () => {\n    setFormData({\n      ...formData,\n      items: [...formData.items, { product: '', quantity: 1, unitPrice: 0 }]\n    });\n  };\n\n  const removeItemRow = (index: number) => {\n    const updated = formData.items.filter((_, i) => i !== index);\n    setFormData({ ...formData, items: updated });\n  };\n\n  const updateItem = (index: number, field: string, value: any) => {\n    const updated = [...formData.items];\n    updated[index] = { ...updated[index], [field]: value };\n    \n    // Auto-fill price when product is selected\n    if (field === 'product') {\n      const selectedProduct = products.find(p => p._id === value);\n      if (selectedProduct) {\n        updated[index].unitPrice = selectedProduct.price;\n      }\n    }\n    \n    setFormData({ ...formData, items: updated });\n  };\n\n  const getPaymentStatusColor = (status: string) => {\n    switch (status) {\n      case 'paid': return 'success';\n      case 'pending': return 'warning';\n      case 'partial': return 'info';\n      case 'refunded': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const columns: GridColDef[] = [\n    { field: 'saleNumber', headerName: 'Sale Number', width: 150 },\n    {\n      field: 'date',\n      headerName: 'Date',\n      width: 120,\n      valueFormatter: (params: any) => format(new Date(params.value), 'MMM dd, yyyy')\n    },\n    {\n      field: 'customer',\n      headerName: 'Customer',\n      width: 200,\n      valueGetter: (params: any) => params.row.customer.name || 'Walk-in Customer'\n    },\n    {\n      field: 'totalAmount',\n      headerName: 'Total Amount',\n      width: 120,\n      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`\n    },\n    { field: 'paymentMethod', headerName: 'Payment Method', width: 130 },\n    {\n      field: 'paymentStatus',\n      headerName: 'Status',\n      width: 120,\n      renderCell: (params: any) => (\n        <Chip\n          label={params.value}\n          color={getPaymentStatusColor(params.value) as any}\n          size=\"small\"\n        />\n      )\n    },\n    { field: 'salesPerson', headerName: 'Sales Person', width: 150 },\n    {\n      field: 'actions',\n      type: 'actions',\n      headerName: 'Actions',\n      width: 100,\n      getActions: (params: any) => [\n        <GridActionsCellItem\n          icon={<ReceiptIcon />}\n          label=\"View Receipt\"\n          onClick={() => console.log('View receipt for', params.row._id)}\n          key=\"receipt\"\n        />\n      ]\n    }\n  ];\n\n  // Chart colors\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];\n\n  const calculateSubtotal = () => {\n    return formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);\n  };\n\n  const calculateTotal = () => {\n    const subtotal = calculateSubtotal();\n    const taxAmount = (subtotal * formData.tax.rate) / 100;\n    return subtotal + taxAmount - formData.discount;\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Sales Management\n      </Typography>\n\n      {/* Summary Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <SalesIcon color=\"primary\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">{dashboard?.summary?.totalSales || 0}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Total Sales\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <MoneyIcon color=\"success\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">${dashboard?.summary?.totalRevenue?.toFixed(2) || 0}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Total Revenue\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <TrendingUpIcon color=\"info\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">${dashboard?.summary?.avgSaleValue?.toFixed(2) || 0}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Average Sale Value\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\">\n                <ReceiptIcon color=\"warning\" sx={{ mr: 2 }} />\n                <Box>\n                  <Typography variant=\"h6\">${dashboard?.summary?.totalTax?.toFixed(2) || 0}</Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Total Tax Collected\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Charts */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Daily Sales Trend\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={dashboard?.dailyTrend || []}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Line type=\"monotone\" dataKey=\"dailyRevenue\" stroke=\"#8884d8\" name=\"Revenue\" />\n                <Line type=\"monotone\" dataKey=\"dailySales\" stroke=\"#82ca9d\" name=\"Sales Count\" />\n              </LineChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Payment Methods\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={dashboard?.paymentMethodBreakdown || []}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ _id, count }) => `${_id}: ${count}`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"count\"\n                >\n                  {(dashboard?.paymentMethodBreakdown || []).map((entry: any, index: number) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Top Products */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Top Selling Products\n        </Typography>\n        <Grid container spacing={2}>\n          {(dashboard?.topProducts || []).slice(0, 5).map((product: any, index: number) => (\n            <Grid item xs={12} sm={6} md={2.4} key={product._id}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    {product.productName}\n                  </Typography>\n                  <Typography variant=\"h6\" color=\"primary\">\n                    ${product.totalRevenue.toFixed(2)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    {product.totalQuantity} units sold\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Paper>\n\n      {/* Actions */}\n      <Box sx={{ mb: 3 }}>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => setOpenDialog(true)}\n        >\n          Record New Sale\n        </Button>\n      </Box>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Sales Data Grid */}\n      <Paper sx={{ height: 600, width: '100%' }}>\n        <DataGrid\n          rows={sales}\n          columns={columns}\n          getRowId={(row) => row._id}\n          loading={loading}\n          pageSizeOptions={[25, 50, 100]}\n          initialState={{\n            pagination: {\n              paginationModel: { page: 0, pageSize: 25 },\n            },\n          }}\n          disableRowSelectionOnClick\n        />\n      </Paper>\n\n      {/* Create Sale Dialog */}\n      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Record New Sale</DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            {/* Customer Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom>Customer Information</Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Customer Name\"\n                value={formData.customer.name}\n                onChange={(e) => setFormData({ \n                  ...formData, \n                  customer: { ...formData.customer, name: e.target.value }\n                })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Email\"\n                type=\"email\"\n                value={formData.customer.email}\n                onChange={(e) => setFormData({ \n                  ...formData, \n                  customer: { ...formData.customer, email: e.target.value }\n                })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Phone\"\n                value={formData.customer.phone}\n                onChange={(e) => setFormData({ \n                  ...formData, \n                  customer: { ...formData.customer, phone: e.target.value }\n                })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Sales Person\"\n                value={formData.salesPerson}\n                onChange={(e) => setFormData({ ...formData, salesPerson: e.target.value })}\n              />\n            </Grid>\n          </Grid>\n\n          {/* Sale Items */}\n          <Accordion sx={{ mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography>Sale Items</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              {formData.items.map((item, index) => (\n                <Grid container spacing={2} key={index} sx={{ mb: 2 }} alignItems=\"center\">\n                  <Grid item xs={4}>\n                    <FormControl fullWidth size=\"small\">\n                      <InputLabel>Product</InputLabel>\n                      <Select\n                        value={item.product}\n                        label=\"Product\"\n                        onChange={(e) => updateItem(index, 'product', e.target.value)}\n                      >\n                        {products.map((product) => (\n                          <MenuItem key={product._id} value={product._id}>\n                            {product.name} (Stock: {product.currentStock})\n                          </MenuItem>\n                        ))}\n                      </Select>\n                    </FormControl>\n                  </Grid>\n                  <Grid item xs={2}>\n                    <TextField\n                      fullWidth\n                      size=\"small\"\n                      label=\"Quantity\"\n                      type=\"number\"\n                      value={item.quantity}\n                      onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}\n                    />\n                  </Grid>\n                  <Grid item xs={3}>\n                    <TextField\n                      fullWidth\n                      size=\"small\"\n                      label=\"Unit Price\"\n                      type=\"number\"\n                      value={item.unitPrice}\n                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                    />\n                  </Grid>\n                  <Grid item xs={2}>\n                    <Typography variant=\"body2\">\n                      ${(item.quantity * item.unitPrice).toFixed(2)}\n                    </Typography>\n                  </Grid>\n                  <Grid item xs={1}>\n                    <IconButton \n                      size=\"small\" \n                      onClick={() => removeItemRow(index)}\n                      disabled={formData.items.length === 1}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Grid>\n                </Grid>\n              ))}\n              <Button onClick={addItemRow} size=\"small\">\n                Add Item\n              </Button>\n            </AccordionDetails>\n          </Accordion>\n\n          {/* Payment and Totals */}\n          <Grid container spacing={2} sx={{ mt: 2 }}>\n            <Grid item xs={6}>\n              <FormControl fullWidth>\n                <InputLabel>Payment Method</InputLabel>\n                <Select\n                  value={formData.paymentMethod}\n                  label=\"Payment Method\"\n                  onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value as any })}\n                >\n                  <MenuItem value=\"cash\">Cash</MenuItem>\n                  <MenuItem value=\"card\">Card</MenuItem>\n                  <MenuItem value=\"bank_transfer\">Bank Transfer</MenuItem>\n                  <MenuItem value=\"check\">Check</MenuItem>\n                  <MenuItem value=\"credit\">Credit</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Tax Rate (%)\"\n                type=\"number\"\n                value={formData.tax.rate}\n                onChange={(e) => setFormData({ \n                  ...formData, \n                  tax: { ...formData.tax, rate: parseFloat(e.target.value) || 0 }\n                })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Discount\"\n                type=\"number\"\n                value={formData.discount}\n                onChange={(e) => setFormData({ ...formData, discount: parseFloat(e.target.value) || 0 })}\n              />\n            </Grid>\n            <Grid item xs={6}>\n              <TextField\n                fullWidth\n                label=\"Total Amount\"\n                value={`$${calculateTotal().toFixed(2)}`}\n                InputProps={{ readOnly: true }}\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>\n          <Button onClick={handleCreateSale} variant=\"contained\">\n            Record Sale\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default SalesTab;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,UAAU,QACL,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,SAAS,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,QAAQ,EAAcC,mBAAmB,QAAQ,kBAAkB;AAC5E,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AAC1H,SAASC,QAAQ,EAAEC,WAAW,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA8BlC,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAM,IAAI,CAAC;EACrD,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC;IACvC8E,QAAQ,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAE,CAAC,CAAC;IACnDC,aAAa,EAAE,MAAgE;IAC/EC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF5F,SAAS,CAAC,MAAM;IACd6F,UAAU,CAAC,CAAC;IACZC,aAAa,CAAC,CAAC;IACfC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0B,QAAQ,GAAG,MAAM/C,QAAQ,CAACgD,MAAM,CAAC,CAAC;MACxCjC,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACnC,KAAK,CAAC;IAC/B,CAAC,CAAC,OAAOoC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjB7B,QAAQ,CAAC,EAAA4B,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,uBAAuB,CAAC;IAClE,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM9C,WAAW,CAAC+C,MAAM,CAAC,CAAC;MAC3C/B,WAAW,CAAC8B,QAAQ,CAACE,IAAI,CAACjC,QAAQ,CAAC;IACrC,CAAC,CAAC,OAAOkC,GAAQ,EAAE;MACjBI,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAE4B,GAAG,CAAC;IACjD;EACF,CAAC;EAED,MAAMJ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM/C,QAAQ,CAACuD,YAAY,CAAC,CAAC;MAC9CpC,YAAY,CAAC4B,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBI,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAE4B,GAAG,CAAC;IAClD;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMxD,QAAQ,CAACyD,MAAM,CAAC/B,QAAQ,CAAC;MAC/BD,aAAa,CAAC,KAAK,CAAC;MACpBiC,SAAS,CAAC,CAAC;MACXd,UAAU,CAAC,CAAC;MACZE,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOI,GAAQ,EAAE;MAAA,IAAAS,cAAA,EAAAC,mBAAA;MACjBrC,QAAQ,CAAC,EAAAoC,cAAA,GAAAT,GAAG,CAACH,QAAQ,cAAAY,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBP,OAAO,KAAI,uBAAuB,CAAC;IAClE;EACF,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB/B,WAAW,CAAC;MACVC,QAAQ,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC;MACzDC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAC,CAAC;MACnDC,aAAa,EAAE,MAAM;MACrBC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC3BC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBlC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXO,KAAK,EAAE,CAAC,GAAGP,QAAQ,CAACO,KAAK,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0B,aAAa,GAAIC,KAAa,IAAK;IACvC,MAAMC,OAAO,GAAGtC,QAAQ,CAACO,KAAK,CAACgC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC5DpC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEO,KAAK,EAAE+B;IAAQ,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMI,UAAU,GAAGA,CAACL,KAAa,EAAEM,KAAa,EAAEC,KAAU,KAAK;IAC/D,MAAMN,OAAO,GAAG,CAAC,GAAGtC,QAAQ,CAACO,KAAK,CAAC;IACnC+B,OAAO,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,OAAO,CAACD,KAAK,CAAC;MAAE,CAACM,KAAK,GAAGC;IAAM,CAAC;;IAEtD;IACA,IAAID,KAAK,KAAK,SAAS,EAAE;MACvB,MAAME,eAAe,GAAGvD,QAAQ,CAACwD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAAC;MAC3D,IAAIC,eAAe,EAAE;QACnBP,OAAO,CAACD,KAAK,CAAC,CAAC3B,SAAS,GAAGmC,eAAe,CAACI,KAAK;MAClD;IACF;IAEAhD,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEO,KAAK,EAAE+B;IAAQ,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMY,qBAAqB,GAAIC,MAAc,IAAK;IAChD,QAAQA,MAAM;MACZ,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,UAAU;QAAE,OAAO,OAAO;MAC/B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,OAAqB,GAAG,CAC5B;IAAET,KAAK,EAAE,YAAY;IAAEU,UAAU,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAI,CAAC,EAC9D;IACEX,KAAK,EAAE,MAAM;IACbU,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,GAAG;IACVC,cAAc,EAAGC,MAAW,IAAKhF,MAAM,CAAC,IAAIiF,IAAI,CAACD,MAAM,CAACZ,KAAK,CAAC,EAAE,cAAc;EAChF,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBU,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE,GAAG;IACVI,WAAW,EAAGF,MAAW,IAAKA,MAAM,CAACG,GAAG,CAACzD,QAAQ,CAACC,IAAI,IAAI;EAC5D,CAAC,EACD;IACEwC,KAAK,EAAE,aAAa;IACpBU,UAAU,EAAE,cAAc;IAC1BC,KAAK,EAAE,GAAG;IACVC,cAAc,EAAGC,MAAW,IAAK,IAAIA,MAAM,CAACZ,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,EACD;IAAEjB,KAAK,EAAE,eAAe;IAAEU,UAAU,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAI,CAAC,EACpE;IACEX,KAAK,EAAE,eAAe;IACtBU,UAAU,EAAE,QAAQ;IACpBC,KAAK,EAAE,GAAG;IACVO,UAAU,EAAGL,MAAW,iBACtB9E,OAAA,CAAC7C,IAAI;MACHiI,KAAK,EAAEN,MAAM,CAACZ,KAAM;MACpBmB,KAAK,EAAEb,qBAAqB,CAACM,MAAM,CAACZ,KAAK,CAAS;MAClDoB,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAEL,CAAC,EACD;IAAEzB,KAAK,EAAE,aAAa;IAAEU,UAAU,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAI,CAAC,EAChE;IACEX,KAAK,EAAE,SAAS;IAChB0B,IAAI,EAAE,SAAS;IACfhB,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,GAAG;IACVgB,UAAU,EAAGd,MAAW,IAAK,cAC3B9E,OAAA,CAACf,mBAAmB;MAClB4G,IAAI,eAAE7F,OAAA,CAACrB,WAAW;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACtBN,KAAK,EAAC,cAAc;MACpBU,OAAO,EAAEA,CAAA,KAAM5C,OAAO,CAAC6C,GAAG,CAAC,kBAAkB,EAAEjB,MAAM,CAACG,GAAG,CAACX,GAAG;IAAE,GAC3D,SAAS;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEN,CAAC,CACF;;EAED;EACA,MAAMM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEtE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAO3E,QAAQ,CAACO,KAAK,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAIC,IAAI,CAACrE,QAAQ,GAAGqE,IAAI,CAACpE,SAAU,EAAE,CAAC,CAAC;EACxF,CAAC;EAED,MAAMqE,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,QAAQ,GAAGL,iBAAiB,CAAC,CAAC;IACpC,MAAMM,SAAS,GAAID,QAAQ,GAAGhF,QAAQ,CAACc,GAAG,CAACC,IAAI,GAAI,GAAG;IACtD,OAAOiE,QAAQ,GAAGC,SAAS,GAAGjF,QAAQ,CAACiB,QAAQ;EACjD,CAAC;EAED,oBACEvC,OAAA,CAACpD,GAAG;IAAA4J,QAAA,gBACFxG,OAAA,CAAClD,UAAU;MAAC2J,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb1F,OAAA,CAAChD,IAAI;MAAC2J,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACxCxG,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BxG,OAAA,CAAC/C,IAAI;UAAAuJ,QAAA,eACHxG,OAAA,CAAC9C,WAAW;YAAAsJ,QAAA,eACVxG,OAAA,CAACpD,GAAG;cAACsK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCxG,OAAA,CAAC3B,SAAS;gBAACgH,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5C1F,OAAA,CAACpD,GAAG;gBAAA4J,QAAA,gBACFxG,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAAAD,QAAA,EAAE,CAAA1F,SAAS,aAATA,SAAS,wBAAAX,kBAAA,GAATW,SAAS,CAAEuG,OAAO,cAAAlH,kBAAA,uBAAlBA,kBAAA,CAAoBmH,UAAU,KAAI;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3E1F,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACpB,KAAK,EAAC,eAAe;kBAAAmB,QAAA,EAAC;gBAElD;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP1F,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BxG,OAAA,CAAC/C,IAAI;UAAAuJ,QAAA,eACHxG,OAAA,CAAC9C,WAAW;YAAAsJ,QAAA,eACVxG,OAAA,CAACpD,GAAG;cAACsK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCxG,OAAA,CAACvB,SAAS;gBAAC4G,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5C1F,OAAA,CAACpD,GAAG;gBAAA4J,QAAA,gBACFxG,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAAAD,QAAA,GAAC,GAAC,EAAC,CAAA1F,SAAS,aAATA,SAAS,wBAAAV,mBAAA,GAATU,SAAS,CAAEuG,OAAO,cAAAjH,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBmH,YAAY,cAAAlH,qBAAA,uBAAhCA,qBAAA,CAAkC6E,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1F1F,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACpB,KAAK,EAAC,eAAe;kBAAAmB,QAAA,EAAC;gBAElD;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP1F,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BxG,OAAA,CAAC/C,IAAI;UAAAuJ,QAAA,eACHxG,OAAA,CAAC9C,WAAW;YAAAsJ,QAAA,eACVxG,OAAA,CAACpD,GAAG;cAACsK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCxG,OAAA,CAACzB,cAAc;gBAAC8G,KAAK,EAAC,MAAM;gBAACwB,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9C1F,OAAA,CAACpD,GAAG;gBAAA4J,QAAA,gBACFxG,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAAAD,QAAA,GAAC,GAAC,EAAC,CAAA1F,SAAS,aAATA,SAAS,wBAAAR,mBAAA,GAATQ,SAAS,CAAEuG,OAAO,cAAA/G,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBkH,YAAY,cAAAjH,qBAAA,uBAAhCA,qBAAA,CAAkC2E,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1F1F,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACpB,KAAK,EAAC,eAAe;kBAAAmB,QAAA,EAAC;gBAElD;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP1F,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,eAC9BxG,OAAA,CAAC/C,IAAI;UAAAuJ,QAAA,eACHxG,OAAA,CAAC9C,WAAW;YAAAsJ,QAAA,eACVxG,OAAA,CAACpD,GAAG;cAACsK,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACrCxG,OAAA,CAACrB,WAAW;gBAAC0G,KAAK,EAAC,SAAS;gBAACwB,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9C1F,OAAA,CAACpD,GAAG;gBAAA4J,QAAA,gBACFxG,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAAAD,QAAA,GAAC,GAAC,EAAC,CAAA1F,SAAS,aAATA,SAAS,wBAAAN,mBAAA,GAATM,SAAS,CAAEuG,OAAO,cAAA7G,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBiH,QAAQ,cAAAhH,qBAAA,uBAA5BA,qBAAA,CAA8ByE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACtF1F,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACpB,KAAK,EAAC,eAAe;kBAAAmB,QAAA,EAAC;gBAElD;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1F,OAAA,CAAChD,IAAI;MAAC2J,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACxCxG,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACvBxG,OAAA,CAACnD,KAAK;UAACgK,EAAE,EAAE;YAAExC,CAAC,EAAE;UAAE,CAAE;UAAAmC,QAAA,gBAClBxG,OAAA,CAAClD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1F,OAAA,CAACR,mBAAmB;YAACoF,KAAK,EAAC,MAAM;YAAC8C,MAAM,EAAE,GAAI;YAAAlB,QAAA,eAC5CxG,OAAA,CAACd,SAAS;cAAC2D,IAAI,EAAE,CAAA/B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE6G,UAAU,KAAI,EAAG;cAAAnB,QAAA,gBAC3CxG,OAAA,CAACV,aAAa;gBAACsI,eAAe,EAAC;cAAK;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC1F,OAAA,CAACZ,KAAK;gBAACyI,OAAO,EAAC;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB1F,OAAA,CAACX,KAAK;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT1F,OAAA,CAACT,OAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1F,OAAA,CAACb,IAAI;gBAACwG,IAAI,EAAC,UAAU;gBAACkC,OAAO,EAAC,cAAc;gBAACC,MAAM,EAAC,SAAS;gBAACrG,IAAI,EAAC;cAAS;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/E1F,OAAA,CAACb,IAAI;gBAACwG,IAAI,EAAC,UAAU;gBAACkC,OAAO,EAAC,YAAY;gBAACC,MAAM,EAAC,SAAS;gBAACrG,IAAI,EAAC;cAAa;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP1F,OAAA,CAAChD,IAAI;QAACoJ,IAAI;QAACW,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACvBxG,OAAA,CAACnD,KAAK;UAACgK,EAAE,EAAE;YAAExC,CAAC,EAAE;UAAE,CAAE;UAAAmC,QAAA,gBAClBxG,OAAA,CAAClD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1F,OAAA,CAACR,mBAAmB;YAACoF,KAAK,EAAC,MAAM;YAAC8C,MAAM,EAAE,GAAI;YAAAlB,QAAA,eAC5CxG,OAAA,CAACP,QAAQ;cAAA+G,QAAA,gBACPxG,OAAA,CAACN,GAAG;gBACFmD,IAAI,EAAE,CAAA/B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiH,sBAAsB,KAAI,EAAG;gBAC9CC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjB9C,KAAK,EAAEA,CAAC;kBAAEd,GAAG;kBAAE6D;gBAAM,CAAC,KAAK,GAAG7D,GAAG,KAAK6D,KAAK,EAAG;gBAC9CC,WAAW,EAAE,EAAG;gBAChBC,IAAI,EAAC,SAAS;gBACdR,OAAO,EAAC,OAAO;gBAAArB,QAAA,EAEd,CAAC,CAAA1F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiH,sBAAsB,KAAI,EAAE,EAAEO,GAAG,CAAC,CAACC,KAAU,EAAE5E,KAAa,kBACvE3D,OAAA,CAACL,IAAI;kBAAuB0I,IAAI,EAAErC,MAAM,CAACrC,KAAK,GAAGqC,MAAM,CAACwC,MAAM;gBAAE,GAArD,QAAQ7E,KAAK,EAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1F,OAAA,CAACT,OAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1F,OAAA,CAACnD,KAAK;MAACgK,EAAE,EAAE;QAAExC,CAAC,EAAE,CAAC;QAAEyC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzBxG,OAAA,CAAClD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1F,OAAA,CAAChD,IAAI;QAAC2J,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAJ,QAAA,EACxB,CAAC,CAAA1F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE2H,WAAW,KAAI,EAAE,EAAEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACxG,OAAY,EAAE6B,KAAa,kBAC1E3D,OAAA,CAAChD,IAAI;UAACoJ,IAAI;UAACW,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,GAAI;UAAAT,QAAA,eAChCxG,OAAA,CAAC/C,IAAI;YAACwJ,OAAO,EAAC,UAAU;YAAAD,QAAA,eACtBxG,OAAA,CAAC9C,WAAW;cAAAsJ,QAAA,gBACVxG,OAAA,CAAClD,UAAU;gBAAC2J,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EACzC1E,OAAO,CAAC6G;cAAW;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACb1F,OAAA,CAAClD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACpB,KAAK,EAAC,SAAS;gBAAAmB,QAAA,GAAC,GACtC,EAAC1E,OAAO,CAACyF,YAAY,CAACrC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACb1F,OAAA,CAAClD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACpB,KAAK,EAAC,eAAe;gBAAAmB,QAAA,GAC9C1E,OAAO,CAAC8G,aAAa,EAAC,aACzB;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GAb+B5D,OAAO,CAACwC,GAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAc7C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1F,OAAA,CAACpD,GAAG;MAACiK,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eACjBxG,OAAA,CAACjD,MAAM;QACL0J,OAAO,EAAC,WAAW;QACnBoC,SAAS,eAAE7I,OAAA,CAAC7B,OAAO;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBI,OAAO,EAAEA,CAAA,KAAMzE,aAAa,CAAC,IAAI,CAAE;QAAAmF,QAAA,EACpC;MAED;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLxE,KAAK,iBACJlB,OAAA,CAACnC,KAAK;MAACiL,QAAQ,EAAC,OAAO;MAACjC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAACiC,OAAO,EAAEA,CAAA,KAAM5H,QAAQ,CAAC,IAAI,CAAE;MAAAqF,QAAA,EAClEtF;IAAK;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD1F,OAAA,CAACnD,KAAK;MAACgK,EAAE,EAAE;QAAEa,MAAM,EAAE,GAAG;QAAE9C,KAAK,EAAE;MAAO,CAAE;MAAA4B,QAAA,eACxCxG,OAAA,CAAChB,QAAQ;QACPgK,IAAI,EAAEtI,KAAM;QACZgE,OAAO,EAAEA,OAAQ;QACjBuE,QAAQ,EAAGhE,GAAG,IAAKA,GAAG,CAACX,GAAI;QAC3BtD,OAAO,EAAEA,OAAQ;QACjBkI,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QAC/BC,YAAY,EAAE;UACZC,UAAU,EAAE;YACVC,eAAe,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAG;UAC3C;QACF,CAAE;QACFC,0BAA0B;MAAA;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGR1F,OAAA,CAAC5C,MAAM;MAACqM,IAAI,EAAErI,UAAW;MAAC2H,OAAO,EAAEA,CAAA,KAAM1H,aAAa,CAAC,KAAK,CAAE;MAACqI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAnD,QAAA,gBACpFxG,OAAA,CAAC3C,WAAW;QAAAmJ,QAAA,EAAC;MAAe;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1C1F,OAAA,CAAC1C,aAAa;QAAAkJ,QAAA,gBACZxG,OAAA,CAAChD,IAAI;UAAC2J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBAExCxG,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,EAAG;YAAAP,QAAA,eAChBxG,OAAA,CAAClD,UAAU;cAAC2J,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAF,QAAA,EAAC;YAAoB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,eAAe;cACrBlB,KAAK,EAAE5C,QAAQ,CAACE,QAAQ,CAACC,IAAK;cAC9BoI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAC3B,GAAGD,QAAQ;gBACXE,QAAQ,EAAE;kBAAE,GAAGF,QAAQ,CAACE,QAAQ;kBAAEC,IAAI,EAAEqI,CAAC,CAACC,MAAM,CAAC7F;gBAAM;cACzD,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,OAAO;cACbO,IAAI,EAAC,OAAO;cACZzB,KAAK,EAAE5C,QAAQ,CAACE,QAAQ,CAACE,KAAM;cAC/BmI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAC3B,GAAGD,QAAQ;gBACXE,QAAQ,EAAE;kBAAE,GAAGF,QAAQ,CAACE,QAAQ;kBAAEE,KAAK,EAAEoI,CAAC,CAACC,MAAM,CAAC7F;gBAAM;cAC1D,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,OAAO;cACblB,KAAK,EAAE5C,QAAQ,CAACE,QAAQ,CAACG,KAAM;cAC/BkI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAC3B,GAAGD,QAAQ;gBACXE,QAAQ,EAAE;kBAAE,GAAGF,QAAQ,CAACE,QAAQ;kBAAEG,KAAK,EAAEmI,CAAC,CAACC,MAAM,CAAC7F;gBAAM;cAC1D,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,cAAc;cACpBlB,KAAK,EAAE5C,QAAQ,CAACY,WAAY;cAC5B2H,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEY,WAAW,EAAE4H,CAAC,CAACC,MAAM,CAAC7F;cAAM,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1F,OAAA,CAAClC,SAAS;UAAC+I,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACvBxG,OAAA,CAACjC,gBAAgB;YAACiM,UAAU,eAAEhK,OAAA,CAACnB,cAAc;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAc,QAAA,eAC/CxG,OAAA,CAAClD,UAAU;cAAA0J,QAAA,EAAC;YAAU;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACnB1F,OAAA,CAAChC,gBAAgB;YAAAwI,QAAA,GACdlF,QAAQ,CAACO,KAAK,CAACyG,GAAG,CAAC,CAAClC,IAAI,EAAEzC,KAAK,kBAC9B3D,OAAA,CAAChD,IAAI;cAAC2J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAaC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAACK,UAAU,EAAC,QAAQ;cAAAX,QAAA,gBACxExG,OAAA,CAAChD,IAAI;gBAACoJ,IAAI;gBAACW,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACfxG,OAAA,CAACvC,WAAW;kBAACkM,SAAS;kBAACrE,IAAI,EAAC,OAAO;kBAAAkB,QAAA,gBACjCxG,OAAA,CAACtC,UAAU;oBAAA8I,QAAA,EAAC;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChC1F,OAAA,CAACrC,MAAM;oBACLuG,KAAK,EAAEkC,IAAI,CAACtE,OAAQ;oBACpBsD,KAAK,EAAC,SAAS;oBACfyE,QAAQ,EAAGC,CAAC,IAAK9F,UAAU,CAACL,KAAK,EAAE,SAAS,EAAEmG,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;oBAAAsC,QAAA,EAE7D5F,QAAQ,CAAC0H,GAAG,CAAExG,OAAO,iBACpB9B,OAAA,CAACpC,QAAQ;sBAAmBsG,KAAK,EAAEpC,OAAO,CAACwC,GAAI;sBAAAkC,QAAA,GAC5C1E,OAAO,CAACL,IAAI,EAAC,WAAS,EAACK,OAAO,CAACmI,YAAY,EAAC,GAC/C;oBAAA,GAFenI,OAAO,CAACwC,GAAG;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEhB,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACP1F,OAAA,CAAChD,IAAI;gBAACoJ,IAAI;gBAACW,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;kBACRmM,SAAS;kBACTrE,IAAI,EAAC,OAAO;kBACZF,KAAK,EAAC,UAAU;kBAChBO,IAAI,EAAC,QAAQ;kBACbzB,KAAK,EAAEkC,IAAI,CAACrE,QAAS;kBACrB8H,QAAQ,EAAGC,CAAC,IAAK9F,UAAU,CAACL,KAAK,EAAE,UAAU,EAAEuG,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAC,IAAI,CAAC;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;gBAACoJ,IAAI;gBAACW,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;kBACRmM,SAAS;kBACTrE,IAAI,EAAC,OAAO;kBACZF,KAAK,EAAC,YAAY;kBAClBO,IAAI,EAAC,QAAQ;kBACbzB,KAAK,EAAEkC,IAAI,CAACpE,SAAU;kBACtB6H,QAAQ,EAAGC,CAAC,IAAK9F,UAAU,CAACL,KAAK,EAAE,WAAW,EAAEuG,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAC,IAAI,CAAC;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;gBAACoJ,IAAI;gBAACW,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACfxG,OAAA,CAAClD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAAAD,QAAA,GAAC,GACzB,EAAC,CAACJ,IAAI,CAACrE,QAAQ,GAAGqE,IAAI,CAACpE,SAAS,EAAEkD,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACP1F,OAAA,CAAChD,IAAI;gBAACoJ,IAAI;gBAACW,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACfxG,OAAA,CAAC/B,UAAU;kBACTqH,IAAI,EAAC,OAAO;kBACZQ,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACC,KAAK,CAAE;kBACpCwG,QAAQ,EAAE7I,QAAQ,CAACO,KAAK,CAAC2G,MAAM,KAAK,CAAE;kBAAAhC,QAAA,eAEtCxG,OAAA,CAACjB,UAAU;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GAlDwB/B,KAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDhC,CACP,CAAC,eACF1F,OAAA,CAACjD,MAAM;cAAC+I,OAAO,EAAErC,UAAW;cAAC6B,IAAI,EAAC,OAAO;cAAAkB,QAAA,EAAC;YAE1C;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGZ1F,OAAA,CAAChD,IAAI;UAAC2J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACxCxG,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACvC,WAAW;cAACkM,SAAS;cAAAnD,QAAA,gBACpBxG,OAAA,CAACtC,UAAU;gBAAA8I,QAAA,EAAC;cAAc;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC1F,OAAA,CAACrC,MAAM;gBACLuG,KAAK,EAAE5C,QAAQ,CAACW,aAAc;gBAC9BmD,KAAK,EAAC,gBAAgB;gBACtByE,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEW,aAAa,EAAE6H,CAAC,CAACC,MAAM,CAAC7F;gBAAa,CAAC,CAAE;gBAAAsC,QAAA,gBAEpFxG,OAAA,CAACpC,QAAQ;kBAACsG,KAAK,EAAC,MAAM;kBAAAsC,QAAA,EAAC;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtC1F,OAAA,CAACpC,QAAQ;kBAACsG,KAAK,EAAC,MAAM;kBAAAsC,QAAA,EAAC;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtC1F,OAAA,CAACpC,QAAQ;kBAACsG,KAAK,EAAC,eAAe;kBAAAsC,QAAA,EAAC;gBAAa;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxD1F,OAAA,CAACpC,QAAQ;kBAACsG,KAAK,EAAC,OAAO;kBAAAsC,QAAA,EAAC;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC1F,OAAA,CAACpC,QAAQ;kBAACsG,KAAK,EAAC,QAAQ;kBAAAsC,QAAA,EAAC;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,cAAc;cACpBO,IAAI,EAAC,QAAQ;cACbzB,KAAK,EAAE5C,QAAQ,CAACc,GAAG,CAACC,IAAK;cACzBwH,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAC3B,GAAGD,QAAQ;gBACXc,GAAG,EAAE;kBAAE,GAAGd,QAAQ,CAACc,GAAG;kBAAEC,IAAI,EAAE6H,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAC,IAAI;gBAAE;cAChE,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,UAAU;cAChBO,IAAI,EAAC,QAAQ;cACbzB,KAAK,EAAE5C,QAAQ,CAACiB,QAAS;cACzBsH,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEiB,QAAQ,EAAE2H,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAC,IAAI;cAAE,CAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAAChD,IAAI;YAACoJ,IAAI;YAACW,EAAE,EAAE,CAAE;YAAAP,QAAA,eACfxG,OAAA,CAACxC,SAAS;cACRmM,SAAS;cACTvE,KAAK,EAAC,cAAc;cACpBlB,KAAK,EAAE,IAAImC,cAAc,CAAC,CAAC,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAG;cACzCkF,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAK;YAAE;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB1F,OAAA,CAACzC,aAAa;QAAAiJ,QAAA,gBACZxG,OAAA,CAACjD,MAAM;UAAC+I,OAAO,EAAEA,CAAA,KAAMzE,aAAa,CAAC,KAAK,CAAE;UAAAmF,QAAA,EAAC;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5D1F,OAAA,CAACjD,MAAM;UAAC+I,OAAO,EAAE1C,gBAAiB;UAACqD,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAEvD;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxF,EAAA,CA3hBID,QAAkB;AAAAqK,EAAA,GAAlBrK,QAAkB;AA6hBxB,eAAeA,QAAQ;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}