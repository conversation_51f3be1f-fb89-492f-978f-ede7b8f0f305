{"ast": null, "code": "// @ts-nocheck\nimport { io } from 'socket.io-client';\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.listeners = new Map();\n  }\n  connect() {\n    var _this$socket;\n    if ((_this$socket = this.socket) !== null && _this$socket !== void 0 && _this$socket.connected) {\n      return this.socket;\n    }\n    this.socket = io(SOCKET_URL, {\n      transports: ['websocket'],\n      autoConnect: true\n    });\n    this.socket.on('connect', () => {\n      console.log('Connected to server');\n    });\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n    this.socket.on('connect_error', error => {\n      console.error('Connection error:', error);\n    });\n    return this.socket;\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  // Generic event listener\n  on(event, callback) {\n    if (!this.socket) {\n      this.connect();\n    }\n    this.socket.on(event, callback);\n\n    // Store listener for cleanup\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event).push(callback);\n  }\n\n  // Remove event listener\n  off(event, callback) {\n    if (this.socket) {\n      this.socket.off(event, callback);\n    }\n\n    // Remove from stored listeners\n    if (this.listeners.has(event)) {\n      const callbacks = this.listeners.get(event);\n      const index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    }\n  }\n\n  // Remove all listeners for an event\n  removeAllListeners(event) {\n    if (this.socket) {\n      this.socket.removeAllListeners(event);\n    }\n    this.listeners.delete(event);\n  }\n\n  // Emit event\n  emit(event, data) {\n    if (this.socket) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Product events\n  onProductCreated(callback) {\n    this.on('product:created', callback);\n  }\n  onProductUpdated(callback) {\n    this.on('product:updated', callback);\n  }\n  onProductDeleted(callback) {\n    this.on('product:deleted', callback);\n  }\n  onProductStockUpdated(callback) {\n    this.on('product:stock_updated', callback);\n  }\n\n  // Category events\n  onCategoryCreated(callback) {\n    this.on('category:created', callback);\n  }\n  onCategoryUpdated(callback) {\n    this.on('category:updated', callback);\n  }\n  onCategoryDeleted(callback) {\n    this.on('category:deleted', callback);\n  }\n\n  // Factory events\n  onFactoryLogCreated(callback) {\n    this.on('factory:log_created', callback);\n  }\n  onFactoryLogUpdated(callback) {\n    this.on('factory:log_updated', callback);\n  }\n  onFactoryLogDeleted(callback) {\n    this.on('factory:log_deleted', callback);\n  }\n\n  // Sales events\n  onSaleCreated(callback) {\n    this.on('sale:created', callback);\n  }\n  onSaleUpdated(callback) {\n    this.on('sale:updated', callback);\n  }\n  onSaleDeleted(callback) {\n    this.on('sale:deleted', callback);\n  }\n  onSaleRefunded(callback) {\n    this.on('sale:refunded', callback);\n  }\n\n  // Order events\n  onOrderCreated(callback) {\n    this.on('order:created', callback);\n  }\n  onOrderUpdated(callback) {\n    this.on('order:updated', callback);\n  }\n  onOrderDeleted(callback) {\n    this.on('order:deleted', callback);\n  }\n  onOrderStatusUpdated(callback) {\n    this.on('order:status_updated', callback);\n  }\n\n  // Webhook events\n  onWebhookCreated(callback) {\n    this.on('webhook:created', callback);\n  }\n  onWebhookUpdated(callback) {\n    this.on('webhook:updated', callback);\n  }\n  onWebhookDeleted(callback) {\n    this.on('webhook:deleted', callback);\n  }\n  onWebhookToggled(callback) {\n    this.on('webhook:toggled', callback);\n  }\n\n  // Cleanup all listeners\n  cleanup() {\n    this.listeners.forEach((callbacks, event) => {\n      this.removeAllListeners(event);\n    });\n    this.listeners.clear();\n  }\n\n  // Get connection status\n  isConnected() {\n    var _this$socket2;\n    return ((_this$socket2 = this.socket) === null || _this$socket2 === void 0 ? void 0 : _this$socket2.connected) || false;\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\nexport default socketService;", "map": {"version": 3, "names": ["io", "SOCKET_URL", "process", "env", "REACT_APP_SOCKET_URL", "SocketService", "constructor", "socket", "listeners", "Map", "connect", "_this$socket", "connected", "transports", "autoConnect", "on", "console", "log", "error", "disconnect", "event", "callback", "has", "set", "get", "push", "off", "callbacks", "index", "indexOf", "splice", "removeAllListeners", "delete", "emit", "data", "onProductCreated", "onProductUpdated", "onProductDeleted", "onProductStockUpdated", "onCategoryCreated", "onCategoryUpdated", "onCategoryDeleted", "onFactoryLogCreated", "onFactoryLogUpdated", "onFactoryLogDeleted", "onSaleCreated", "onSaleUpdated", "onSaleDeleted", "onSaleRefunded", "onOrderCreated", "onOrderUpdated", "onOrderDeleted", "onOrderStatusUpdated", "onWebhookCreated", "onWebhookUpdated", "onWebhookDeleted", "onWebhookToggled", "cleanup", "for<PERSON>ach", "clear", "isConnected", "_this$socket2", "socketService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/JusSamy/frontend/src/services/socket.ts"], "sourcesContent": ["// @ts-nocheck\nimport { io } from 'socket.io-client';\n\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.listeners = new Map();\n  }\n\n  connect() {\n    if (this.socket?.connected) {\n      return this.socket;\n    }\n\n    this.socket = io(SOCKET_URL, {\n      transports: ['websocket'],\n      autoConnect: true,\n    });\n\n    this.socket.on('connect', () => {\n      console.log('Connected to server');\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Connection error:', error);\n    });\n\n    return this.socket;\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  // Generic event listener\n  on(event, callback) {\n    if (!this.socket) {\n      this.connect();\n    }\n\n    this.socket.on(event, callback);\n\n    // Store listener for cleanup\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event).push(callback);\n  }\n\n  // Remove event listener\n  off(event, callback) {\n    if (this.socket) {\n      this.socket.off(event, callback);\n    }\n\n    // Remove from stored listeners\n    if (this.listeners.has(event)) {\n      const callbacks = this.listeners.get(event);\n      const index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    }\n  }\n\n  // Remove all listeners for an event\n  removeAllListeners(event) {\n    if (this.socket) {\n      this.socket.removeAllListeners(event);\n    }\n    this.listeners.delete(event);\n  }\n\n  // Emit event\n  emit(event, data) {\n    if (this.socket) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Product events\n  onProductCreated(callback) {\n    this.on('product:created', callback);\n  }\n\n  onProductUpdated(callback) {\n    this.on('product:updated', callback);\n  }\n\n  onProductDeleted(callback) {\n    this.on('product:deleted', callback);\n  }\n\n  onProductStockUpdated(callback) {\n    this.on('product:stock_updated', callback);\n  }\n\n  // Category events\n  onCategoryCreated(callback) {\n    this.on('category:created', callback);\n  }\n\n  onCategoryUpdated(callback) {\n    this.on('category:updated', callback);\n  }\n\n  onCategoryDeleted(callback) {\n    this.on('category:deleted', callback);\n  }\n\n  // Factory events\n  onFactoryLogCreated(callback) {\n    this.on('factory:log_created', callback);\n  }\n\n  onFactoryLogUpdated(callback) {\n    this.on('factory:log_updated', callback);\n  }\n\n  onFactoryLogDeleted(callback) {\n    this.on('factory:log_deleted', callback);\n  }\n\n  // Sales events\n  onSaleCreated(callback) {\n    this.on('sale:created', callback);\n  }\n\n  onSaleUpdated(callback) {\n    this.on('sale:updated', callback);\n  }\n\n  onSaleDeleted(callback) {\n    this.on('sale:deleted', callback);\n  }\n\n  onSaleRefunded(callback) {\n    this.on('sale:refunded', callback);\n  }\n\n  // Order events\n  onOrderCreated(callback) {\n    this.on('order:created', callback);\n  }\n\n  onOrderUpdated(callback) {\n    this.on('order:updated', callback);\n  }\n\n  onOrderDeleted(callback) {\n    this.on('order:deleted', callback);\n  }\n\n  onOrderStatusUpdated(callback) {\n    this.on('order:status_updated', callback);\n  }\n\n  // Webhook events\n  onWebhookCreated(callback) {\n    this.on('webhook:created', callback);\n  }\n\n  onWebhookUpdated(callback) {\n    this.on('webhook:updated', callback);\n  }\n\n  onWebhookDeleted(callback) {\n    this.on('webhook:deleted', callback);\n  }\n\n  onWebhookToggled(callback) {\n    this.on('webhook:toggled', callback);\n  }\n\n  // Cleanup all listeners\n  cleanup() {\n    this.listeners.forEach((callbacks, event) => {\n      this.removeAllListeners(event);\n    });\n    this.listeners.clear();\n  }\n\n  // Get connection status\n  isConnected() {\n    return this.socket?.connected || false;\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "mappings": "AAAA;AACA,SAASA,EAAE,QAAQ,kBAAkB;AAErC,MAAMC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB;AAE9E,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;EAEAC,OAAOA,CAAA,EAAG;IAAA,IAAAC,YAAA;IACR,KAAAA,YAAA,GAAI,IAAI,CAACJ,MAAM,cAAAI,YAAA,eAAXA,YAAA,CAAaC,SAAS,EAAE;MAC1B,OAAO,IAAI,CAACL,MAAM;IACpB;IAEA,IAAI,CAACA,MAAM,GAAGP,EAAE,CAACC,UAAU,EAAE;MAC3BY,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,IAAI,CAACP,MAAM,CAACQ,EAAE,CAAC,SAAS,EAAE,MAAM;MAC9BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,CAAC;IAEF,IAAI,CAACV,MAAM,CAACQ,EAAE,CAAC,YAAY,EAAE,MAAM;MACjCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC;IAEF,IAAI,CAACV,MAAM,CAACQ,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;MACzCF,OAAO,CAACE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACX,MAAM;EACpB;EAEAY,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACZ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACY,UAAU,CAAC,CAAC;MACxB,IAAI,CAACZ,MAAM,GAAG,IAAI;IACpB;EACF;;EAEA;EACAQ,EAAEA,CAACK,KAAK,EAAEC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACd,MAAM,EAAE;MAChB,IAAI,CAACG,OAAO,CAAC,CAAC;IAChB;IAEA,IAAI,CAACH,MAAM,CAACQ,EAAE,CAACK,KAAK,EAAEC,QAAQ,CAAC;;IAE/B;IACA,IAAI,CAAC,IAAI,CAACb,SAAS,CAACc,GAAG,CAACF,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACZ,SAAS,CAACe,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;IAC/B;IACA,IAAI,CAACZ,SAAS,CAACgB,GAAG,CAACJ,KAAK,CAAC,CAACK,IAAI,CAACJ,QAAQ,CAAC;EAC1C;;EAEA;EACAK,GAAGA,CAACN,KAAK,EAAEC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACd,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACmB,GAAG,CAACN,KAAK,EAAEC,QAAQ,CAAC;IAClC;;IAEA;IACA,IAAI,IAAI,CAACb,SAAS,CAACc,GAAG,CAACF,KAAK,CAAC,EAAE;MAC7B,MAAMO,SAAS,GAAG,IAAI,CAACnB,SAAS,CAACgB,GAAG,CAACJ,KAAK,CAAC;MAC3C,MAAMQ,KAAK,GAAGD,SAAS,CAACE,OAAO,CAACR,QAAQ,CAAC;MACzC,IAAIO,KAAK,GAAG,CAAC,CAAC,EAAE;QACdD,SAAS,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B;IACF;EACF;;EAEA;EACAG,kBAAkBA,CAACX,KAAK,EAAE;IACxB,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,kBAAkB,CAACX,KAAK,CAAC;IACvC;IACA,IAAI,CAACZ,SAAS,CAACwB,MAAM,CAACZ,KAAK,CAAC;EAC9B;;EAEA;EACAa,IAAIA,CAACb,KAAK,EAAEc,IAAI,EAAE;IAChB,IAAI,IAAI,CAAC3B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC0B,IAAI,CAACb,KAAK,EAAEc,IAAI,CAAC;IAC/B;EACF;;EAEA;EACAC,gBAAgBA,CAACd,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAe,gBAAgBA,CAACf,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAgB,gBAAgBA,CAAChB,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAiB,qBAAqBA,CAACjB,QAAQ,EAAE;IAC9B,IAAI,CAACN,EAAE,CAAC,uBAAuB,EAAEM,QAAQ,CAAC;EAC5C;;EAEA;EACAkB,iBAAiBA,CAAClB,QAAQ,EAAE;IAC1B,IAAI,CAACN,EAAE,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;EACvC;EAEAmB,iBAAiBA,CAACnB,QAAQ,EAAE;IAC1B,IAAI,CAACN,EAAE,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;EACvC;EAEAoB,iBAAiBA,CAACpB,QAAQ,EAAE;IAC1B,IAAI,CAACN,EAAE,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;EACvC;;EAEA;EACAqB,mBAAmBA,CAACrB,QAAQ,EAAE;IAC5B,IAAI,CAACN,EAAE,CAAC,qBAAqB,EAAEM,QAAQ,CAAC;EAC1C;EAEAsB,mBAAmBA,CAACtB,QAAQ,EAAE;IAC5B,IAAI,CAACN,EAAE,CAAC,qBAAqB,EAAEM,QAAQ,CAAC;EAC1C;EAEAuB,mBAAmBA,CAACvB,QAAQ,EAAE;IAC5B,IAAI,CAACN,EAAE,CAAC,qBAAqB,EAAEM,QAAQ,CAAC;EAC1C;;EAEA;EACAwB,aAAaA,CAACxB,QAAQ,EAAE;IACtB,IAAI,CAACN,EAAE,CAAC,cAAc,EAAEM,QAAQ,CAAC;EACnC;EAEAyB,aAAaA,CAACzB,QAAQ,EAAE;IACtB,IAAI,CAACN,EAAE,CAAC,cAAc,EAAEM,QAAQ,CAAC;EACnC;EAEA0B,aAAaA,CAAC1B,QAAQ,EAAE;IACtB,IAAI,CAACN,EAAE,CAAC,cAAc,EAAEM,QAAQ,CAAC;EACnC;EAEA2B,cAAcA,CAAC3B,QAAQ,EAAE;IACvB,IAAI,CAACN,EAAE,CAAC,eAAe,EAAEM,QAAQ,CAAC;EACpC;;EAEA;EACA4B,cAAcA,CAAC5B,QAAQ,EAAE;IACvB,IAAI,CAACN,EAAE,CAAC,eAAe,EAAEM,QAAQ,CAAC;EACpC;EAEA6B,cAAcA,CAAC7B,QAAQ,EAAE;IACvB,IAAI,CAACN,EAAE,CAAC,eAAe,EAAEM,QAAQ,CAAC;EACpC;EAEA8B,cAAcA,CAAC9B,QAAQ,EAAE;IACvB,IAAI,CAACN,EAAE,CAAC,eAAe,EAAEM,QAAQ,CAAC;EACpC;EAEA+B,oBAAoBA,CAAC/B,QAAQ,EAAE;IAC7B,IAAI,CAACN,EAAE,CAAC,sBAAsB,EAAEM,QAAQ,CAAC;EAC3C;;EAEA;EACAgC,gBAAgBA,CAAChC,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAiC,gBAAgBA,CAACjC,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAkC,gBAAgBA,CAAClC,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;EAEAmC,gBAAgBA,CAACnC,QAAQ,EAAE;IACzB,IAAI,CAACN,EAAE,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;EACtC;;EAEA;EACAoC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACjD,SAAS,CAACkD,OAAO,CAAC,CAAC/B,SAAS,EAAEP,KAAK,KAAK;MAC3C,IAAI,CAACW,kBAAkB,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAACZ,SAAS,CAACmD,KAAK,CAAC,CAAC;EACxB;;EAEA;EACAC,WAAWA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACZ,OAAO,EAAAA,aAAA,OAAI,CAACtD,MAAM,cAAAsD,aAAA,uBAAXA,aAAA,CAAajD,SAAS,KAAI,KAAK;EACxC;AACF;;AAEA;AACA,MAAMkD,aAAa,GAAG,IAAIzD,aAAa,CAAC,CAAC;AAEzC,eAAeyD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}