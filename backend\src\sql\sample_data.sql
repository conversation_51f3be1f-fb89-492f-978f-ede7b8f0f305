-- Sample data for JusSamy Inventory Management System

-- Insert Categories
INSERT INTO categories (name, description, unit_type, unit_label) VALUES
('Beverages', 'Fruit juices and drinks', 'volume', 'Liters'),
('Raw Materials', 'Fruits and ingredients for juice production', 'weight', 'KG'),
('Packaging', 'Bottles, caps, and labels', 'unit', 'Units'),
('Cleaning Supplies', 'Sanitizers and cleaning materials', 'volume', 'Liters'),
('Equipment Parts', 'Machine parts and maintenance supplies', 'unit', 'Units');

-- Insert Products
INSERT INTO products (name, description, sku, barcode, category_id, price, cost, current_stock, min_stock_level, is_finished_product) VALUES
-- Finished Products (Beverages)
('Apple Juice 1L', 'Fresh apple juice in 1L bottles', 'APJ-1L-001', '1234567890123', 1, 2.50, 1.20, 150, 20, TRUE),
('Orange Juice 1L', 'Fresh orange juice in 1L bottles', 'ORJ-1L-001', '1234567890124', 1, 2.75, 1.35, 120, 20, TRUE),
('Mixed Fruit Juice 1L', 'Blend of apple, orange, and grape', 'MFJ-1L-001', '1234567890125', 1, 3.00, 1.50, 80, 15, TRUE),
('Apple Juice 500ml', 'Fresh apple juice in 500ml bottles', 'APJ-500-001', '1234567890126', 1, 1.50, 0.75, 200, 30, TRUE),
('Orange Juice 500ml', 'Fresh orange juice in 500ml bottles', 'ORJ-500-001', '1234567890127', 1, 1.65, 0.80, 180, 30, TRUE),

-- Raw Materials
('Fresh Apples', 'Grade A apples for juice production', 'RAW-APP-001', '2234567890123', 2, 0.00, 1.50, 500, 100, FALSE),
('Fresh Oranges', 'Grade A oranges for juice production', 'RAW-ORG-001', '2234567890124', 2, 0.00, 1.80, 400, 80, FALSE),
('Fresh Grapes', 'Grade A grapes for juice production', 'RAW-GRP-001', '2234567890125', 2, 0.00, 2.20, 200, 50, FALSE),
('Sugar', 'White refined sugar', 'RAW-SUG-001', '2234567890126', 2, 0.00, 0.80, 300, 50, FALSE),
('Citric Acid', 'Food grade citric acid', 'RAW-CIT-001', '2234567890127', 2, 0.00, 3.50, 50, 10, FALSE),

-- Packaging Materials
('1L Glass Bottles', 'Clear glass bottles for 1L products', 'PKG-BOT-1L', '3234567890123', 3, 0.00, 0.35, 1000, 200, FALSE),
('500ml Glass Bottles', 'Clear glass bottles for 500ml products', 'PKG-BOT-500', '3234567890124', 3, 0.00, 0.25, 1500, 300, FALSE),
('Bottle Caps', 'Metal caps for glass bottles', 'PKG-CAP-001', '3234567890125', 3, 0.00, 0.05, 2000, 500, FALSE),
('Product Labels', 'Waterproof labels for bottles', 'PKG-LBL-001', '3234567890126', 3, 0.00, 0.08, 1800, 400, FALSE),
('Cardboard Boxes', '12-bottle shipping boxes', 'PKG-BOX-12', '3234567890127', 3, 0.00, 0.50, 500, 100, FALSE),

-- Cleaning Supplies
('Industrial Sanitizer', 'Food-grade sanitizing solution', 'CLN-SAN-001', '*************', 4, 0.00, 8.50, 25, 5, FALSE),
('Equipment Cleaner', 'Specialized equipment cleaning solution', 'CLN-EQP-001', '*************', 4, 0.00, 12.00, 15, 3, FALSE),

-- Equipment Parts
('Pump Seals', 'Replacement seals for juice pumps', 'EQP-SEAL-001', '5234567890123', 5, 0.00, 15.00, 20, 5, FALSE),
('Filter Cartridges', 'Water filtration cartridges', 'EQP-FILT-001', '5234567890124', 5, 0.00, 25.00, 12, 3, FALSE),
('Conveyor Belts', 'Replacement conveyor belts', 'EQP-BELT-001', '5234567890125', 5, 0.00, 150.00, 3, 1, FALSE);

-- Insert Customers
INSERT INTO customers (name, email, phone, address, city, postal_code) VALUES
('SuperMarket Chain Ltd', '<EMAIL>', '+216-71-123-456', '123 Commerce Street', 'Tunis', '1000'),
('Fresh Foods Distribution', '<EMAIL>', '+216-71-234-567', '456 Industrial Zone', 'Sfax', '3000'),
('Local Grocery Store', '<EMAIL>', '+216-71-345-678', '789 Main Street', 'Sousse', '4000'),
('Restaurant Le Jardin', '<EMAIL>', '+216-71-456-789', '321 Restaurant Row', 'Tunis', '1001'),
('Café Central', '<EMAIL>', '+216-71-567-890', '654 City Center', 'Monastir', '5000'),
('Hotel Prestige', '<EMAIL>', '+216-71-678-901', '987 Hotel District', 'Hammamet', '8000'),
('School Cafeteria Services', '<EMAIL>', '+216-71-789-012', '147 Education Zone', 'Kairouan', '3100'),
('Juice Bar Express', '<EMAIL>', '+216-71-890-123', '258 Shopping Mall', 'Tunis', '1002');

-- Insert Sample Sales
INSERT INTO sales (sale_number, customer_id, customer_name, customer_phone, total_amount, payment_method, payment_status, sales_person, sale_date) VALUES
('SALE-********-0001', 1, 'SuperMarket Chain Ltd', '+216-71-123-456', 125.00, 'bank_transfer', 'paid', 'Ahmed Ben Ali', '2025-07-09 08:30:00'),
('SALE-********-0002', 3, 'Local Grocery Store', '+216-71-345-678', 67.50, 'cash', 'paid', 'Fatma Trabelsi', '2025-07-09 10:15:00'),
('SALE-********-0003', NULL, 'Walk-in Customer', '+216-98-123-456', 15.75, 'cash', 'paid', 'Ahmed Ben Ali', '2025-07-09 14:20:00'),
('SALE-********-0001', 2, 'Fresh Foods Distribution', '+216-71-234-567', 340.00, 'bank_transfer', 'paid', 'Fatma Trabelsi', '2025-07-08 16:45:00'),
('SALE-********-0002', 4, 'Restaurant Le Jardin', '+216-71-456-789', 89.25, 'card', 'paid', 'Ahmed Ben Ali', '2025-07-08 11:30:00');

-- Insert Sample Sale Items
INSERT INTO sale_items (sale_id, product_id, product_name, quantity, unit_price, total_price, final_price) VALUES
-- Sale 1: SuperMarket Chain Ltd
(1, 1, 'Apple Juice 1L', 30, 2.50, 75.00, 75.00),
(1, 2, 'Orange Juice 1L', 20, 2.75, 55.00, 55.00),
-- Sale 2: Local Grocery Store  
(2, 4, 'Apple Juice 500ml', 25, 1.50, 37.50, 37.50),
(2, 5, 'Orange Juice 500ml', 20, 1.65, 33.00, 33.00),
-- Sale 3: Walk-in Customer
(3, 1, 'Apple Juice 1L', 3, 2.50, 7.50, 7.50),
(3, 4, 'Apple Juice 500ml', 5, 1.50, 7.50, 7.50),
-- Sale 4: Fresh Foods Distribution
(4, 1, 'Apple Juice 1L', 80, 2.50, 200.00, 200.00),
(4, 2, 'Orange Juice 1L', 60, 2.75, 165.00, 165.00),
-- Sale 5: Restaurant Le Jardin
(5, 3, 'Mixed Fruit Juice 1L', 25, 3.00, 75.00, 75.00),
(5, 5, 'Orange Juice 500ml', 10, 1.65, 16.50, 16.50);

-- Insert Sample Orders
INSERT INTO orders (order_number, customer_id, customer_name, customer_phone, customer_address, total_amount, status, priority, order_date, delivery_date, notes) VALUES
('ORD-20250710-0001', 1, 'SuperMarket Chain Ltd', '+216-71-123-456', '123 Commerce Street, Tunis', 450.00, 'confirmed', 'high', '2025-07-09 09:00:00', '2025-07-12 10:00:00', 'Weekly bulk order'),
('ORD-20250710-0002', 6, 'Hotel Prestige', '+216-71-678-901', '987 Hotel District, Hammamet', 280.00, 'pending', 'normal', '2025-07-09 11:30:00', '2025-07-15 14:00:00', 'For hotel restaurant'),
('ORD-20250710-0003', 7, 'School Cafeteria Services', '+216-71-789-012', '147 Education Zone, Kairouan', 195.00, 'in_production', 'urgent', '2025-07-09 13:15:00', '2025-07-11 08:00:00', 'School lunch program'),
('ORD-20250710-0004', 8, 'Juice Bar Express', '+216-71-890-123', '258 Shopping Mall, Tunis', 320.00, 'pending', 'normal', '2025-07-09 15:45:00', '2025-07-16 12:00:00', 'New juice bar opening');

-- Insert Sample Order Items
INSERT INTO order_items (order_id, product_id, product_name, quantity, unit_price, total_price) VALUES
-- Order 1: SuperMarket Chain Ltd
(1, 1, 'Apple Juice 1L', 100, 2.50, 250.00),
(1, 2, 'Orange Juice 1L', 80, 2.75, 220.00),
-- Order 2: Hotel Prestige
(2, 3, 'Mixed Fruit Juice 1L', 60, 3.00, 180.00),
(2, 1, 'Apple Juice 1L', 40, 2.50, 100.00),
-- Order 3: School Cafeteria Services
(3, 4, 'Apple Juice 500ml', 80, 1.50, 120.00),
(3, 5, 'Orange Juice 500ml', 50, 1.65, 82.50),
-- Order 4: Juice Bar Express
(4, 1, 'Apple Juice 1L', 60, 2.50, 150.00),
(4, 2, 'Orange Juice 1L', 50, 2.75, 137.50),
(4, 3, 'Mixed Fruit Juice 1L', 20, 3.00, 60.00);

-- Insert Sample Factory Logs
INSERT INTO factory_logs (type, batch_number, operator, shift, efficiency, total_cost, total_value, profit_loss, log_date, notes) VALUES
('production', 'BATCH-********-001', 'Mohamed Sassi', 'morning', 92.5, 180.00, 225.00, 45.00, '2025-07-09 06:00:00', 'Apple juice production run'),
('production', 'BATCH-********-002', 'Amina Khelifi', 'afternoon', 88.0, 210.00, 247.50, 37.50, '2025-07-09 14:00:00', 'Orange juice production run'),
('production', 'BATCH-********-001', 'Mohamed Sassi', 'morning', 95.0, 165.00, 202.50, 37.50, '2025-07-08 06:30:00', 'Mixed fruit juice production'),
('consumption', 'MAINT-********-001', 'Technician Ali', 'night', 100.0, 45.00, 0.00, -45.00, '2025-07-08 22:00:00', 'Equipment maintenance and cleaning');

-- Insert Factory Log Materials
INSERT INTO factory_log_materials (factory_log_id, product_id, product_name, quantity, unit, cost) VALUES
-- Batch 1: Apple juice production
(1, 6, 'Fresh Apples', 120, 'KG', 180.00),
-- Batch 2: Orange juice production  
(2, 7, 'Fresh Oranges', 140, 'KG', 252.00),
-- Batch 3: Mixed fruit juice
(3, 6, 'Fresh Apples', 60, 'KG', 90.00),
(3, 7, 'Fresh Oranges', 50, 'KG', 90.00),
(3, 8, 'Fresh Grapes', 30, 'KG', 66.00),
-- Maintenance consumption
(4, 16, 'Industrial Sanitizer', 5, 'Liters', 42.50);

-- Insert Factory Log Products
INSERT INTO factory_log_products (factory_log_id, product_id, product_name, quantity, unit, estimated_value) VALUES
-- Batch 1: Apple juice production
(1, 1, 'Apple Juice 1L', 90, 'Bottles', 225.00),
-- Batch 2: Orange juice production
(2, 2, 'Orange Juice 1L', 95, 'Bottles', 261.25),
-- Batch 3: Mixed fruit juice
(3, 3, 'Mixed Fruit Juice 1L', 75, 'Bottles', 225.00);

-- Insert Sample Webhooks
INSERT INTO webhooks (name, url, secret, events, is_active, success_count, failure_count) VALUES
('Inventory Alert System', 'https://alerts.jussamy.com/webhook', 'webhook_secret_123', '["stock.low", "stock.critical", "order.urgent"]', TRUE, 45, 2),
('Unity Mobile App', 'https://unity-backend.jussamy.com/webhooks/inventory', 'unity_webhook_secret', '["unity.stock.low", "unity.order.created", "unity.sale.completed"]', TRUE, 128, 5),
('Accounting Integration', 'https://accounting.jussamy.com/api/webhooks', 'accounting_secret_456', '["sale.created", "order.completed", "production.finished"]', TRUE, 89, 1),
('Slack Notifications', '*****************************************************************************', '', '["stock.critical", "order.urgent", "system.error"]', FALSE, 0, 0);
