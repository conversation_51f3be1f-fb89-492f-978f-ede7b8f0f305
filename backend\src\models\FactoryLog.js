const { executeQuery } = require('../config/database');

class FactoryLog {
  static find(conditions = {}) {
    const queryBuilder = {
      sort: () => queryBuilder,
      limit: () => queryBuilder,
      skip: () => queryBuilder,
      populate: () => queryBuilder,
      exec: async () => [],
      then: (resolve) => resolve([])
    };
    return queryBuilder;
  }

  static async countDocuments() { return 0; }
  static async findById() { return null; }
}

module.exports = FactoryLog;
