using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;

namespace JusSamy.SDK
{
    /// <summary>
    /// JusSamy Unity SDK for inventory management integration
    /// </summary>
    public class JusSamySDK : MonoBehaviour
    {
        [Header("API Configuration")]
        public string baseUrl = "http://localhost:5000/api/unity";
        public string apiKey = "your-unity-api-key";
        
        [Header("Settings")]
        public bool enableDebugLogs = true;
        public float requestTimeout = 30f;

        // Events for real-time updates
        public static event Action<List<Product>> OnProductsUpdated;
        public static event Action<List<Product>> OnLowStockAlert;
        public static event Action<Order> OnOrderCreated;
        public static event Action<Sale> OnSaleCompleted;
        public static event Action<string> OnError;

        private static JusSamySDK instance;
        public static JusSamySDK Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<JusSamySDK>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("JusSamySDK");
                        instance = go.AddComponent<JusSamySDK>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (instance != this)
            {
                Destroy(gameObject);
            }
        }

        #region API Methods

        /// <summary>
        /// Get all products from the inventory
        /// </summary>
        public void GetProducts(Action<List<Product>> onSuccess = null, Action<string> onError = null)
        {
            StartCoroutine(GetProductsCoroutine(onSuccess, onError));
        }

        /// <summary>
        /// Get products with low stock
        /// </summary>
        public void GetLowStockProducts(Action<LowStockResponse> onSuccess = null, Action<string> onError = null)
        {
            StartCoroutine(GetLowStockProductsCoroutine(onSuccess, onError));
        }

        /// <summary>
        /// Record a new sale
        /// </summary>
        public void RecordSale(SaleRequest saleData, Action<SaleResponse> onSuccess = null, Action<string> onError = null)
        {
            StartCoroutine(RecordSaleCoroutine(saleData, onSuccess, onError));
        }

        /// <summary>
        /// Create a new order
        /// </summary>
        public void CreateOrder(OrderRequest orderData, Action<OrderResponse> onSuccess = null, Action<string> onError = null)
        {
            StartCoroutine(CreateOrderCoroutine(orderData, onSuccess, onError));
        }

        /// <summary>
        /// Get dashboard data
        /// </summary>
        public void GetDashboard(Action<DashboardResponse> onSuccess = null, Action<string> onError = null)
        {
            StartCoroutine(GetDashboardCoroutine(onSuccess, onError));
        }

        #endregion

        #region Coroutines

        private IEnumerator GetProductsCoroutine(Action<List<Product>> onSuccess, Action<string> onError)
        {
            using (UnityWebRequest request = CreateGetRequest("/products"))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        ProductsResponse response = JsonUtility.FromJson<ProductsResponse>(request.downloadHandler.text);
                        if (response.success)
                        {
                            LogDebug($"Retrieved {response.data.Count} products");
                            onSuccess?.Invoke(response.data);
                            OnProductsUpdated?.Invoke(response.data);
                        }
                        else
                        {
                            string error = response.message ?? "Unknown error";
                            LogError($"API Error: {error}");
                            onError?.Invoke(error);
                        }
                    }
                    catch (Exception e)
                    {
                        LogError($"JSON Parse Error: {e.Message}");
                        onError?.Invoke($"Failed to parse response: {e.Message}");
                    }
                }
                else
                {
                    string error = $"HTTP Error: {request.error}";
                    LogError(error);
                    onError?.Invoke(error);
                    OnError?.Invoke(error);
                }
            }
        }

        private IEnumerator GetLowStockProductsCoroutine(Action<LowStockResponse> onSuccess, Action<string> onError)
        {
            using (UnityWebRequest request = CreateGetRequest("/products/low-stock"))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        LowStockResponse response = JsonUtility.FromJson<LowStockResponse>(request.downloadHandler.text);
                        if (response.success)
                        {
                            LogDebug($"Found {response.data.Count} low stock products");
                            onSuccess?.Invoke(response);
                            OnLowStockAlert?.Invoke(response.data);
                        }
                        else
                        {
                            onError?.Invoke(response.message ?? "Unknown error");
                        }
                    }
                    catch (Exception e)
                    {
                        onError?.Invoke($"Failed to parse response: {e.Message}");
                    }
                }
                else
                {
                    onError?.Invoke($"HTTP Error: {request.error}");
                }
            }
        }

        private IEnumerator RecordSaleCoroutine(SaleRequest saleData, Action<SaleResponse> onSuccess, Action<string> onError)
        {
            string jsonData = JsonUtility.ToJson(saleData);
            using (UnityWebRequest request = CreatePostRequest("/sales", jsonData))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        SaleResponse response = JsonUtility.FromJson<SaleResponse>(request.downloadHandler.text);
                        if (response.success)
                        {
                            LogDebug($"Sale recorded: {response.data.saleNumber}");
                            onSuccess?.Invoke(response);
                            OnSaleCompleted?.Invoke(null); // You might want to pass sale data here
                        }
                        else
                        {
                            onError?.Invoke(response.message ?? "Unknown error");
                        }
                    }
                    catch (Exception e)
                    {
                        onError?.Invoke($"Failed to parse response: {e.Message}");
                    }
                }
                else
                {
                    onError?.Invoke($"HTTP Error: {request.error}");
                }
            }
        }

        private IEnumerator CreateOrderCoroutine(OrderRequest orderData, Action<OrderResponse> onSuccess, Action<string> onError)
        {
            string jsonData = JsonUtility.ToJson(orderData);
            using (UnityWebRequest request = CreatePostRequest("/orders", jsonData))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        OrderResponse response = JsonUtility.FromJson<OrderResponse>(request.downloadHandler.text);
                        if (response.success)
                        {
                            LogDebug($"Order created: {response.data.orderNumber}");
                            onSuccess?.Invoke(response);
                            OnOrderCreated?.Invoke(null); // You might want to pass order data here
                        }
                        else
                        {
                            onError?.Invoke(response.message ?? "Unknown error");
                        }
                    }
                    catch (Exception e)
                    {
                        onError?.Invoke($"Failed to parse response: {e.Message}");
                    }
                }
                else
                {
                    onError?.Invoke($"HTTP Error: {request.error}");
                }
            }
        }

        private IEnumerator GetDashboardCoroutine(Action<DashboardResponse> onSuccess, Action<string> onError)
        {
            using (UnityWebRequest request = CreateGetRequest("/dashboard"))
            {
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        DashboardResponse response = JsonUtility.FromJson<DashboardResponse>(request.downloadHandler.text);
                        if (response.success)
                        {
                            LogDebug("Dashboard data retrieved successfully");
                            onSuccess?.Invoke(response);
                        }
                        else
                        {
                            onError?.Invoke(response.message ?? "Unknown error");
                        }
                    }
                    catch (Exception e)
                    {
                        onError?.Invoke($"Failed to parse response: {e.Message}");
                    }
                }
                else
                {
                    onError?.Invoke($"HTTP Error: {request.error}");
                }
            }
        }

        #endregion

        #region Helper Methods

        private UnityWebRequest CreateGetRequest(string endpoint)
        {
            UnityWebRequest request = UnityWebRequest.Get(baseUrl + endpoint);
            request.SetRequestHeader("X-API-Key", apiKey);
            request.SetRequestHeader("Content-Type", "application/json");
            request.timeout = (int)requestTimeout;
            return request;
        }

        private UnityWebRequest CreatePostRequest(string endpoint, string jsonData)
        {
            UnityWebRequest request = UnityWebRequest.Post(baseUrl + endpoint, jsonData, "application/json");
            request.SetRequestHeader("X-API-Key", apiKey);
            request.timeout = (int)requestTimeout;
            return request;
        }

        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[JusSamySDK] {message}");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"[JusSamySDK] {message}");
        }

        #endregion

        #region Public Utility Methods

        /// <summary>
        /// Check if the API is reachable
        /// </summary>
        public void TestConnection(Action<bool> onResult)
        {
            StartCoroutine(TestConnectionCoroutine(onResult));
        }

        private IEnumerator TestConnectionCoroutine(Action<bool> onResult)
        {
            using (UnityWebRequest request = CreateGetRequest("/dashboard"))
            {
                yield return request.SendWebRequest();
                onResult?.Invoke(request.result == UnityWebRequest.Result.Success);
            }
        }

        /// <summary>
        /// Set API configuration at runtime
        /// </summary>
        public void SetConfiguration(string newBaseUrl, string newApiKey)
        {
            baseUrl = newBaseUrl;
            apiKey = newApiKey;
            LogDebug($"Configuration updated - URL: {baseUrl}");
        }

        #endregion
    }
}
