const express = require('express');
const router = express.Router();
const { Product, Category } = require('../models');
const { body, validationResult } = require('express-validator');
const webhookService = require('../services/webhookService');

// Validation middleware
const validateProduct = [
  body('name').trim().notEmpty().withMessage('Product name is required'),
  body('category').isMongoId().withMessage('Valid category ID is required'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('currentStock').isFloat({ min: 0 }).withMessage('Current stock must be a positive number'),
  body('minStockLevel').optional().isFloat({ min: 0 }).withMessage('Min stock level must be a positive number')
];

// GET /api/products - Get all products
router.get('/', async (req, res) => {
  try {
    const {
      isActive = 'true',
      category,
      stockStatus,
      isFinishedProduct,
      page = 1,
      limit = 50,
      search
    } = req.query;

    const filter = {};
    if (isActive !== 'all') {
      filter.isActive = isActive === 'true';
    }
    if (category) {
      filter.category = category;
    }
    if (isFinishedProduct !== undefined) {
      filter.isFinishedProduct = isFinishedProduct === 'true';
    }
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { barcode: { $regex: search, $options: 'i' } }
      ];
    }

    let query = Product.find(filter).populate('category', 'name unitType unitLabel');

    // Filter by stock status if specified
    if (stockStatus) {
      const products = await query.exec();
      const filteredProducts = products.filter(product => product.stockStatus === stockStatus);
      return res.json({
        products: filteredProducts.slice((page - 1) * limit, page * limit),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(filteredProducts.length / limit),
          total: filteredProducts.length
        }
      });
    }

    const products = await query
      .sort({ name: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Product.countDocuments(filter);

    res.json({
      products,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching products', error: error.message });
  }
});

// GET /api/products/low-stock - Get products with low stock
router.get('/low-stock', async (req, res) => {
  try {
    const products = await Product.find({ isActive: true })
      .populate('category', 'name unitType unitLabel')
      .exec();

    const lowStockProducts = products.filter(product => 
      product.currentStock <= product.minStockLevel
    );

    res.json(lowStockProducts);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching low stock products', error: error.message });
  }
});

// GET /api/products/:id - Get product by ID
router.get('/:id', async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('category', 'name unitType unitLabel')
      .populate('recipe.material', 'name currentStock');
    
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    res.json(product);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching product', error: error.message });
  }
});

// POST /api/products - Create new product
router.post('/', validateProduct, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Verify category exists
    const category = await Category.findById(req.body.category);
    if (!category) {
      return res.status(400).json({ message: 'Category not found' });
    }

    const product = new Product(req.body);
    await product.save();
    await product.populate('category', 'name unitType unitLabel');

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('product:created', product);

    res.status(201).json(product);
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ message: `${field} already exists` });
    }
    res.status(500).json({ message: 'Error creating product', error: error.message });
  }
});

// PUT /api/products/:id - Update product
router.put('/:id', validateProduct, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const oldProduct = await Product.findById(req.params.id);
    if (!oldProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    const product = await Product.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('category', 'name unitType unitLabel');

    // Check for stock level changes and trigger webhooks
    if (oldProduct.currentStock !== product.currentStock) {
      await webhookService.triggerWebhook('stock.updated', {
        product: product,
        oldStock: oldProduct.currentStock,
        newStock: product.currentStock
      });

      // Check for low stock
      if (product.currentStock <= product.minStockLevel) {
        await webhookService.triggerWebhook('stock.low', { product });
      }

      // Check for out of stock
      if (product.currentStock === 0) {
        await webhookService.triggerWebhook('stock.out', { product });
      }
    }

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('product:updated', product);

    res.json(product);
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ message: `${field} already exists` });
    }
    res.status(500).json({ message: 'Error updating product', error: error.message });
  }
});

// PATCH /api/products/:id/stock - Update product stock
router.patch('/:id/stock', async (req, res) => {
  try {
    const { quantity, operation = 'set' } = req.body;
    
    if (typeof quantity !== 'number' || quantity < 0) {
      return res.status(400).json({ message: 'Quantity must be a positive number' });
    }

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    const oldStock = product.currentStock;
    
    switch (operation) {
      case 'add':
        product.currentStock += quantity;
        break;
      case 'subtract':
        product.currentStock = Math.max(0, product.currentStock - quantity);
        break;
      case 'set':
      default:
        product.currentStock = quantity;
        break;
    }

    await product.save();
    await product.populate('category', 'name unitType unitLabel');

    // Trigger webhooks for stock changes
    await webhookService.triggerWebhook('stock.updated', {
      product: product,
      oldStock: oldStock,
      newStock: product.currentStock
    });

    if (product.currentStock <= product.minStockLevel) {
      await webhookService.triggerWebhook('stock.low', { product });
    }

    if (product.currentStock === 0) {
      await webhookService.triggerWebhook('stock.out', { product });
    }

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('product:stock_updated', product);

    res.json(product);
  } catch (error) {
    res.status(500).json({ message: 'Error updating product stock', error: error.message });
  }
});

// DELETE /api/products/:id - Delete product (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const product = await Product.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('product:deleted', { id: req.params.id });

    res.json({ message: 'Product deactivated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting product', error: error.message });
  }
});

module.exports = router;
