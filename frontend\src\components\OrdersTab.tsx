// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>ton,

  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Divider,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Assignment as OrderIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { ordersAPI, productsAPI } from '../services/api';
import { format, addDays } from 'date-fns';

interface Order {
  _id: string;
  orderNumber: string;
  orderDate: string;
  deliveryDate: string;
  status: 'pending' | 'confirmed' | 'in_production' | 'ready' | 'delivered' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  customer: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  items: Array<{
    product: { _id: string; name: string };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  totalAmount: number;
  daysUntilDelivery: number;
  urgencyLevel: string;
  stockAlert?: {
    isTriggered: boolean;
    message: string;
  };
}

interface Product {
  _id: string;
  name: string;
  price: number;
  currentStock: number;
}

const OrdersTab: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [dashboard, setDashboard] = useState<any>(null);
  const [urgentOrders, setUrgentOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Form state for order creation/editing
  const [formData, setFormData] = useState({
    deliveryDate: format(addDays(new Date(), 7), 'yyyy-MM-dd'),
    customer: {
      name: '',
      email: '',
      phone: '',
      address: ''
    },
    items: [{ product: '', quantity: 1, unitPrice: 0 }],
    priority: 'normal' as 'low' | 'normal' | 'high' | 'urgent',
    notes: ''
  });

  useEffect(() => {
    fetchOrders();
    fetchProducts();
    fetchDashboard();
    fetchUrgentOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await ordersAPI.getAll();
      setOrders(response.data.orders);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await productsAPI.getAll();
      setProducts(response.data.products);
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
    }
  };

  const fetchDashboard = async () => {
    try {
      const response = await ordersAPI.getDashboard();
      setDashboard(response.data);
    } catch (err: any) {
      console.error('Failed to fetch dashboard:', err);
    }
  };

  const fetchUrgentOrders = async () => {
    try {
      const response = await ordersAPI.getUrgent();
      setUrgentOrders(response.data);
    } catch (err: any) {
      console.error('Failed to fetch urgent orders:', err);
    }
  };

  const handleCreateOrder = async () => {
    try {
      await ordersAPI.create(formData);
      setOpenDialog(false);
      resetForm();
      fetchOrders();
      fetchDashboard();
      fetchUrgentOrders();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create order');
    }
  };

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return;
    
    try {
      await ordersAPI.update(selectedOrder._id, formData);
      setOpenDialog(false);
      resetForm();
      fetchOrders();
      fetchDashboard();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update order');
    }
  };

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    try {
      await ordersAPI.updateStatus(orderId, newStatus);
      fetchOrders();
      fetchDashboard();
      fetchUrgentOrders();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update order status');
    }
  };

  const resetForm = () => {
    setFormData({
      deliveryDate: format(addDays(new Date(), 7), 'yyyy-MM-dd'),
      customer: { name: '', email: '', phone: '', address: '' },
      items: [{ product: '', quantity: 1, unitPrice: 0 }],
      priority: 'normal',
      notes: ''
    });
    setSelectedOrder(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setOpenDialog(true);
  };

  const openEditDialog = (order: Order) => {
    setSelectedOrder(order);
    setFormData({
      deliveryDate: format(new Date(order.deliveryDate), 'yyyy-MM-dd'),
      customer: order.customer,
      items: order.items.map(item => ({
        product: item.product._id,
        quantity: item.quantity,
        unitPrice: item.unitPrice
      })),
      priority: order.priority,
      notes: ''
    });
    setOpenDialog(true);
  };

  const addItemRow = () => {
    setFormData({
      ...formData,
      items: [...formData.items, { product: '', quantity: 1, unitPrice: 0 }]
    });
  };

  const removeItemRow = (index: number) => {
    const updated = formData.items.filter((_, i) => i !== index);
    setFormData({ ...formData, items: updated });
  };

  const updateItem = (index: number, field: string, value: any) => {
    const updated = [...formData.items];
    updated[index] = { ...updated[index], [field]: value };
    
    // Auto-fill price when product is selected
    if (field === 'product') {
      const selectedProduct = products.find(p => p._id === value);
      if (selectedProduct) {
        updated[index].unitPrice = selectedProduct.price;
      }
    }
    
    setFormData({ ...formData, items: updated });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'confirmed': return 'info';
      case 'in_production': return 'primary';
      case 'ready': return 'success';
      case 'delivered': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'info';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'overdue': return 'error';
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const columns: GridColDef[] = [
    { field: 'orderNumber', headerName: 'Order Number', width: 150 },
    {
      field: 'orderDate',
      headerName: 'Order Date',
      width: 120,
      valueFormatter: (params: any) => format(new Date(params.value), 'MMM dd, yyyy')
    },
    {
      field: 'deliveryDate',
      headerName: 'Delivery Date',
      width: 130,
      valueFormatter: (params: any) => format(new Date(params.value), 'MMM dd, yyyy')
    },
    {
      field: 'customer',
      headerName: 'Customer',
      width: 200,
      valueGetter: (params: any) => params.row.customer.name
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      )
    },
    {
      field: 'priority',
      headerName: 'Priority',
      width: 100,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={getPriorityColor(params.value) as any}
          size="small"
          variant="outlined"
        />
      )
    },
    {
      field: 'urgencyLevel',
      headerName: 'Urgency',
      width: 100,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={getUrgencyColor(params.value) as any}
          size="small"
        />
      )
    },
    {
      field: 'totalAmount',
      headerName: 'Total Amount',
      width: 120,
      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`
    },
    {
      field: 'daysUntilDelivery',
      headerName: 'Days Left',
      width: 100,
      renderCell: (params: any) => (
        <Typography color={params.value < 0 ? 'error' : params.value <= 1 ? 'warning.main' : 'inherit'}>
          {params.value < 0 ? 'Overdue' : `${params.value} days`}
        </Typography>
      )
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 150,
      getActions: (params: any) => [
        <GridActionsCellItem
          icon={<EditIcon />}
          label="Edit"
          onClick={() => openEditDialog(params.row)}
          key="edit"
        />,
        <GridActionsCellItem
          icon={<CheckIcon />}
          label="Update Status"
          onClick={() => {
            const nextStatus = getNextStatus(params.row.status);
            if (nextStatus) {
              handleStatusUpdate(params.row._id, nextStatus);
            }
          }}
          key="status"
        />
      ]
    }
  ];

  const getNextStatus = (currentStatus: string) => {
    const statusFlow = {
      'pending': 'confirmed',
      'confirmed': 'in_production',
      'in_production': 'ready',
      'ready': 'delivered'
    };
    return statusFlow[currentStatus as keyof typeof statusFlow];
  };

  const calculateTotal = () => {
    return formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Orders Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <OrderIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{dashboard?.summary?.totalOrders || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Orders
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ScheduleIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{urgentOrders.length}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Urgent Orders
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WarningIcon color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{dashboard?.overdueOrders?.length || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Overdue Orders
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <CheckIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${dashboard?.summary?.totalValue?.toFixed(2) || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Order Value
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Urgent Orders Alert */}
      {urgentOrders.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Urgent Orders Requiring Attention
          </Typography>
          <List dense>
            {urgentOrders.slice(0, 5).map((order) => (
              <ListItem key={order._id}>
                <ListItemText
                  primary={`${order.orderNumber} - ${order.customer.name}`}
                  secondary={`Delivery: ${format(new Date(order.deliveryDate), 'MMM dd, yyyy')} | ${order.urgencyLevel}`}
                />
              </ListItem>
            ))}
          </List>
        </Alert>
      )}

      {/* Order Status Breakdown */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Order Status Overview
        </Typography>
        <Grid container spacing={2}>
          {(dashboard?.statusBreakdown || []).map((status: any) => (
            <Grid item xs={12} sm={6} md={2.4} key={status._id}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" color="primary">
                    {status.count}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {status._id.charAt(0).toUpperCase() + status._id.slice(1)}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    ${status.totalValue.toFixed(2)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={openCreateDialog}
        >
          Create New Order
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Orders Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={orders}
          columns={columns}
          getRowId={(row) => row._id}
          loading={loading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Create/Edit Order Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedOrder ? 'Edit Order' : 'Create New Order'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Customer Information</Typography>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Customer Name"
                required
                value={formData.customer.name}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, name: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.customer.email}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, email: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Phone"
                required
                value={formData.customer.phone}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, phone: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Address"
                required
                value={formData.customer.address}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, address: e.target.value }
                })}
              />
            </Grid>

            {/* Order Details */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Order Details</Typography>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Delivery Date"
                type="date"
                required
                value={formData.deliveryDate}
                onChange={(e) => setFormData({ ...formData, deliveryDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={formData.priority}
                  label="Priority"
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value as any })}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="urgent">Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Order Items */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Order Items</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {formData.items.map((item, index) => (
                <Grid container spacing={2} key={index} sx={{ mb: 2 }} alignItems="center">
                  <Grid item xs={5}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Product</InputLabel>
                      <Select
                        value={item.product}
                        label="Product"
                        onChange={(e) => updateItem(index, 'product', e.target.value)}
                      >
                        {products.map((product) => (
                          <MenuItem key={product._id} value={product._id}>
                            {product.name} (Stock: {product.currentStock})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Quantity"
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Unit Price"
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <Typography variant="body2">
                      ${(item.quantity * item.unitPrice).toFixed(2)}
                    </Typography>
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton 
                      size="small" 
                      onClick={() => removeItemRow(index)}
                      disabled={formData.items.length === 1}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              ))}
              <Button onClick={addItemRow} size="small">
                Add Item
              </Button>
              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Typography variant="h6">
                  Total: ${calculateTotal().toFixed(2)}
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            onClick={selectedOrder ? handleUpdateOrder : handleCreateOrder}
            variant="contained"
          >
            {selectedOrder ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OrdersTab;
