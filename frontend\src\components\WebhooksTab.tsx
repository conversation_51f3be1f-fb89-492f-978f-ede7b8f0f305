// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Webhook as WebhookIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  PlayArrow as TestIcon,
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { webhooksAPI } from '../services/api';
import { format } from 'date-fns';

interface Webhook {
  _id: string;
  name: string;
  url: string;
  events: string[];
  isActive: boolean;
  successCount: number;
  failureCount: number;
  successRate: number;
  lastTriggered?: string;
  lastStatus?: 'success' | 'failed';
  lastError?: string;
}

interface EventType {
  type: string;
  description: string;
  payload: any;
}

const WebhooksTab: React.FC = () => {
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  const [recentLogs, setRecentLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState<Webhook | null>(null);

  // Form state for webhook creation/editing
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    events: [] as string[],
    secret: '',
    headers: {} as Record<string, string>,
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000
    }
  });

  useEffect(() => {
    fetchWebhooks();
    fetchEventTypes();
    fetchRecentLogs();
  }, []);

  const fetchWebhooks = async () => {
    try {
      setLoading(true);
      const response = await webhooksAPI.getAll();
      setWebhooks(response.data.webhooks);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch webhooks');
    } finally {
      setLoading(false);
    }
  };

  const fetchEventTypes = async () => {
    try {
      const response = await webhooksAPI.getEventTypes();
      setEventTypes(response.data);
    } catch (err: any) {
      console.error('Failed to fetch event types:', err);
    }
  };

  const fetchRecentLogs = async () => {
    try {
      const response = await webhooksAPI.getRecentLogs();
      setRecentLogs(response.data);
    } catch (err: any) {
      console.error('Failed to fetch recent logs:', err);
    }
  };

  const handleCreateWebhook = async () => {
    try {
      await webhooksAPI.create(formData);
      setOpenDialog(false);
      resetForm();
      fetchWebhooks();
      setSuccess('Webhook created successfully');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create webhook');
    }
  };

  const handleUpdateWebhook = async () => {
    if (!selectedWebhook) return;
    
    try {
      await webhooksAPI.update(selectedWebhook._id, formData);
      setOpenDialog(false);
      resetForm();
      fetchWebhooks();
      setSuccess('Webhook updated successfully');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update webhook');
    }
  };

  const handleToggleWebhook = async (webhookId: string) => {
    try {
      await webhooksAPI.toggle(webhookId);
      fetchWebhooks();
      setSuccess('Webhook status updated');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle webhook');
    }
  };

  const handleTestWebhook = async (webhookId: string) => {
    try {
      await webhooksAPI.test(webhookId);
      setSuccess('Test webhook sent successfully');
      fetchWebhooks();
      fetchRecentLogs();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to test webhook');
    }
  };

  const handleDeleteWebhook = async (webhookId: string) => {
    if (window.confirm('Are you sure you want to delete this webhook?')) {
      try {
        await webhooksAPI.delete(webhookId);
        fetchWebhooks();
        setSuccess('Webhook deleted successfully');
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to delete webhook');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      url: '',
      events: [],
      secret: '',
      headers: {},
      retryPolicy: {
        maxRetries: 3,
        retryDelay: 1000
      }
    });
    setSelectedWebhook(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setOpenDialog(true);
  };

  const openEditDialog = (webhook: Webhook) => {
    setSelectedWebhook(webhook);
    setFormData({
      name: webhook.name,
      url: webhook.url,
      events: webhook.events,
      secret: '',
      headers: {},
      retryPolicy: {
        maxRetries: 3,
        retryDelay: 1000
      }
    });
    setOpenDialog(true);
  };

  const columns: GridColDef[] = [
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'url', headerName: 'URL', width: 300 },
    {
      field: 'events',
      headerName: 'Events',
      width: 200,
      renderCell: (params: any) => (
        <Box>
          {params.value.slice(0, 2).map((event: string) => (
            <Chip key={event} label={event} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
          ))}
          {params.value.length > 2 && (
            <Chip label={`+${params.value.length - 2} more`} size="small" variant="outlined" />
          )}
        </Box>
      )
    },
    {
      field: 'isActive',
      headerName: 'Status',
      width: 100,
      renderCell: (params: any) => (
        <Chip
          label={params.value ? 'Active' : 'Inactive'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      )
    },
    {
      field: 'successRate',
      headerName: 'Success Rate',
      width: 120,
      renderCell: (params: any) => (
        <Typography color={params.value >= 90 ? 'success.main' : params.value >= 70 ? 'warning.main' : 'error.main'}>
          {params.value}%
        </Typography>
      )
    },
    {
      field: 'lastTriggered',
      headerName: 'Last Triggered',
      width: 150,
      valueFormatter: (params: any) => params.value ? format(new Date(params.value), 'MMM dd, HH:mm') : 'Never'
    },
    {
      field: 'lastStatus',
      headerName: 'Last Status',
      width: 120,
      renderCell: (params: any) => {
        if (!params.value) return <Typography variant="body2">-</Typography>;
        return (
          <Chip
            label={params.value}
            color={params.value === 'success' ? 'success' : 'error'}
            size="small"
            icon={params.value === 'success' ? <SuccessIcon /> : <ErrorIcon />}
          />
        );
      }
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 200,
      getActions: (params: any) => [
        <GridActionsCellItem
          icon={<EditIcon />}
          label="Edit"
          onClick={() => openEditDialog(params.row)}
          key="edit"
        />,
        <GridActionsCellItem
          icon={params.row.isActive ? <ToggleOffIcon /> : <ToggleOnIcon />}
          label={params.row.isActive ? 'Disable' : 'Enable'}
          onClick={() => handleToggleWebhook(params.row._id)}
          key="toggle"
        />,
        <GridActionsCellItem
          icon={<TestIcon />}
          label="Test"
          onClick={() => handleTestWebhook(params.row._id)}
          key="test"
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => handleDeleteWebhook(params.row._id)}
          key="delete"
        />
      ]
    }
  ];

  // Calculate summary statistics
  const activeWebhooks = webhooks.filter(w => w.isActive).length;
  const totalRequests = webhooks.reduce((sum, w) => sum + w.successCount + w.failureCount, 0);
  const totalSuccesses = webhooks.reduce((sum, w) => sum + w.successCount, 0);
  const overallSuccessRate = totalRequests > 0 ? ((totalSuccesses / totalRequests) * 100).toFixed(1) : 0;

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Webhooks Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WebhookIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{webhooks.length}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Webhooks
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SuccessIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{activeWebhooks}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Active Webhooks
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SuccessIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{totalRequests}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Requests
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SuccessIcon color={Number(overallSuccessRate) >= 90 ? 'success' : 'warning'} sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{overallSuccessRate}%</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Success Rate
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Webhook Activity
            </Typography>
            <List dense>
              {recentLogs.slice(0, 5).map((log, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={log.name}
                    secondary={`Last triggered: ${log.lastTriggered ? format(new Date(log.lastTriggered), 'MMM dd, HH:mm') : 'Never'}`}
                  />
                  <ListItemSecondaryAction>
                    <Chip 
                      label={log.lastStatus || 'Unknown'} 
                      color={log.lastStatus === 'success' ? 'success' : 'error'}
                      size="small"
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Available Event Types
            </Typography>
            <List dense>
              {eventTypes.slice(0, 5).map((eventType) => (
                <ListItem key={eventType.type}>
                  <ListItemText
                    primary={eventType.type}
                    secondary={eventType.description}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={openCreateDialog}
        >
          Create Webhook
        </Button>
      </Box>

      {/* Success/Error Alerts */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Webhooks Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={webhooks}
          columns={columns}
          getRowId={(row) => row._id}
          loading={loading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Create/Edit Webhook Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedWebhook ? 'Edit Webhook' : 'Create New Webhook'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook Name"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook URL"
                required
                type="url"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                helperText="The endpoint URL where webhook payloads will be sent"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Events</InputLabel>
                <Select
                  multiple
                  value={formData.events}
                  label="Events"
                  onChange={(e) => setFormData({ ...formData, events: e.target.value as string[] })}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {eventTypes.map((eventType) => (
                    <MenuItem key={eventType.type} value={eventType.type}>
                      {eventType.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Secret (Optional)"
                type="password"
                value={formData.secret}
                onChange={(e) => setFormData({ ...formData, secret: e.target.value })}
                helperText="Secret key for webhook signature verification"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Max Retries"
                type="number"
                value={formData.retryPolicy.maxRetries}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  retryPolicy: { 
                    ...formData.retryPolicy, 
                    maxRetries: parseInt(e.target.value) || 0 
                  }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Retry Delay (ms)"
                type="number"
                value={formData.retryPolicy.retryDelay}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  retryPolicy: { 
                    ...formData.retryPolicy, 
                    retryDelay: parseInt(e.target.value) || 1000 
                  }
                })}
              />
            </Grid>
          </Grid>

          {/* Event Type Documentation */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Event Types Documentation</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {eventTypes.map((eventType) => (
                  <ListItem key={eventType.type}>
                    <ListItemText
                      primary={eventType.type}
                      secondary={eventType.description}
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            onClick={selectedWebhook ? handleUpdateWebhook : handleCreateWebhook}
            variant="contained"
            disabled={!formData.name || !formData.url || formData.events.length === 0}
          >
            {selectedWebhook ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WebhooksTab;
