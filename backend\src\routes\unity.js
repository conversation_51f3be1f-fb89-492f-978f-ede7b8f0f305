const express = require('express');
const router = express.Router();
const { Product, Sale, Order, FactoryLog } = require('../models');
const { verifyUnityApiKey } = require('../middleware/webhookAuth');
const webhookService = require('../services/webhookService');

// Apply Unity API key verification to all routes
router.use(verifyUnityApiKey);

// GET /api/unity/products - Get products optimized for Unity
router.get('/products', async (req, res) => {
  try {
    const { category, stockStatus, limit = 100 } = req.query;
    
    const filter = { isActive: true };
    if (category) filter.category = category;
    
    const products = await Product.find(filter)
      .populate('category', 'name unitType unitLabel')
      .limit(parseInt(limit))
      .select('name category price currentStock minStockLevel stockStatus isFinishedProduct')
      .lean();

    // Filter by stock status if specified
    let filteredProducts = products;
    if (stockStatus) {
      filteredProducts = products.filter(product => {
        const status = product.currentStock <= product.minStockLevel ? 'low' : 
                      product.currentStock === 0 ? 'out' : 'normal';
        return status === stockStatus;
      });
    }

    // Transform for Unity consumption
    const unityProducts = filteredProducts.map(product => ({
      id: product._id,
      name: product.name,
      categoryName: product.category.name,
      unitType: product.category.unitType,
      unitLabel: product.category.unitLabel,
      price: product.price,
      currentStock: product.currentStock,
      minStockLevel: product.minStockLevel,
      stockStatus: product.currentStock <= product.minStockLevel ? 'low' : 
                   product.currentStock === 0 ? 'out' : 'normal',
      isFinishedProduct: product.isFinishedProduct
    }));

    res.json({
      success: true,
      data: unityProducts,
      count: unityProducts.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching products for Unity',
      error: error.message
    });
  }
});

// GET /api/unity/products/low-stock - Get low stock products for Unity alerts
router.get('/products/low-stock', async (req, res) => {
  try {
    const products = await Product.find({ isActive: true })
      .populate('category', 'name unitLabel')
      .select('name category currentStock minStockLevel')
      .lean();

    const lowStockProducts = products
      .filter(product => product.currentStock <= product.minStockLevel)
      .map(product => ({
        id: product._id,
        name: product.name,
        categoryName: product.category.name,
        currentStock: product.currentStock,
        minStockLevel: product.minStockLevel,
        unitLabel: product.category.unitLabel,
        shortage: product.minStockLevel - product.currentStock
      }));

    res.json({
      success: true,
      data: lowStockProducts,
      alertLevel: lowStockProducts.length > 10 ? 'critical' : 
                  lowStockProducts.length > 5 ? 'warning' : 'normal',
      count: lowStockProducts.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching low stock products',
      error: error.message
    });
  }
});

// POST /api/unity/sales - Record sale from Unity app
router.post('/sales', async (req, res) => {
  try {
    const { items, customer, paymentMethod = 'cash', salesPerson = 'Unity App' } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Items array is required'
      });
    }

    // Verify products exist and have sufficient stock
    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        return res.status(400).json({
          success: false,
          message: `Product ${item.productId} not found`
        });
      }
      if (product.currentStock < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${product.name}. Available: ${product.currentStock}, Required: ${item.quantity}`
        });
      }
    }

    // Create sale items with proper structure
    const saleItems = await Promise.all(items.map(async (item) => {
      const product = await Product.findById(item.productId);
      return {
        product: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice || product.price,
        totalPrice: item.quantity * (item.unitPrice || product.price),
        finalPrice: item.quantity * (item.unitPrice || product.price)
      };
    }));

    const sale = new Sale({
      customer: customer || { name: 'Unity App Customer' },
      items: saleItems,
      paymentMethod,
      salesPerson,
      paymentStatus: 'paid'
    });

    await sale.save();

    // Update product stocks
    for (const item of items) {
      await Product.findByIdAndUpdate(
        item.productId,
        { $inc: { currentStock: -item.quantity } }
      );
    }

    await sale.populate('items.product', 'name');

    // Trigger sale webhook
    await webhookService.triggerWebhook('sale.created', { sale });

    res.status(201).json({
      success: true,
      data: {
        saleId: sale._id,
        saleNumber: sale.saleNumber,
        totalAmount: sale.totalAmount,
        timestamp: sale.date
      },
      message: 'Sale recorded successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error recording sale',
      error: error.message
    });
  }
});

// POST /api/unity/orders - Create order from Unity app
router.post('/orders', async (req, res) => {
  try {
    const { 
      customer, 
      items, 
      deliveryDate, 
      priority = 'normal',
      notes = 'Order created from Unity app'
    } = req.body;

    if (!customer || !customer.name || !customer.phone) {
      return res.status(400).json({
        success: false,
        message: 'Customer name and phone are required'
      });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Items array is required'
      });
    }

    if (!deliveryDate) {
      return res.status(400).json({
        success: false,
        message: 'Delivery date is required'
      });
    }

    // Verify products exist
    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        return res.status(400).json({
          success: false,
          message: `Product ${item.productId} not found`
        });
      }
    }

    // Create order items
    const orderItems = await Promise.all(items.map(async (item) => {
      const product = await Product.findById(item.productId);
      return {
        product: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice || product.price,
        totalPrice: item.quantity * (item.unitPrice || product.price)
      };
    }));

    const order = new Order({
      customer,
      items: orderItems,
      deliveryDate: new Date(deliveryDate),
      priority,
      notes
    });

    await order.save();
    await order.populate('items.product', 'name currentStock');

    // Check stock availability
    for (const item of order.items) {
      const product = await Product.findById(item.product);
      if (product.currentStock < item.quantity) {
        order.stockAlert = {
          isTriggered: true,
          message: `Insufficient stock for ${product.name}. Available: ${product.currentStock}, Required: ${item.quantity}`,
          triggeredAt: new Date()
        };
        await order.save();
        break;
      }
    }

    // Trigger order webhook
    await webhookService.triggerWebhook('order.created', { order });

    res.status(201).json({
      success: true,
      data: {
        orderId: order._id,
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount,
        deliveryDate: order.deliveryDate,
        status: order.status,
        stockAlert: order.stockAlert
      },
      message: 'Order created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating order',
      error: error.message
    });
  }
});

// GET /api/unity/orders - Get orders for Unity app
router.get('/orders', async (req, res) => {
  try {
    const { status, limit = 50, customerPhone } = req.query;
    
    const filter = {};
    if (status) filter.status = status;
    if (customerPhone) filter['customer.phone'] = customerPhone;

    const orders = await Order.find(filter)
      .populate('items.product', 'name')
      .sort({ orderDate: -1 })
      .limit(parseInt(limit))
      .select('orderNumber orderDate deliveryDate status priority customer totalAmount daysUntilDelivery urgencyLevel')
      .lean();

    const unityOrders = orders.map(order => ({
      id: order._id,
      orderNumber: order.orderNumber,
      orderDate: order.orderDate,
      deliveryDate: order.deliveryDate,
      status: order.status,
      priority: order.priority,
      customerName: order.customer.name,
      customerPhone: order.customer.phone,
      totalAmount: order.totalAmount,
      daysUntilDelivery: order.daysUntilDelivery,
      urgencyLevel: order.urgencyLevel
    }));

    res.json({
      success: true,
      data: unityOrders,
      count: unityOrders.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching orders',
      error: error.message
    });
  }
});

// POST /api/unity/factory/log - Record production from Unity app
router.post('/factory/log', async (req, res) => {
  try {
    const {
      type = 'production',
      materialsUsed = [],
      productsGenerated = [],
      operator = 'Unity App',
      shift = 'morning',
      efficiency = 100,
      notes = 'Production logged from Unity app'
    } = req.body;

    const log = new FactoryLog({
      type,
      materialsUsed,
      productsGenerated,
      operator,
      shift,
      efficiency,
      notes,
      batchNumber: `UNITY-${Date.now()}`
    });

    await log.save();

    // Update product stocks if production
    if (type === 'production') {
      // Decrease raw materials
      for (const material of materialsUsed) {
        await Product.findByIdAndUpdate(
          material.product,
          { $inc: { currentStock: -material.quantity } }
        );
      }
      
      // Increase finished products
      for (const product of productsGenerated) {
        await Product.findByIdAndUpdate(
          product.product,
          { $inc: { currentStock: product.quantity } }
        );
      }
    }

    // Trigger production webhook
    await webhookService.triggerWebhook('production.completed', { log });

    res.status(201).json({
      success: true,
      data: {
        logId: log._id,
        batchNumber: log.batchNumber,
        totalCost: log.totalCost,
        totalValue: log.totalValue,
        profitLoss: log.profitLoss
      },
      message: 'Production log recorded successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error recording production log',
      error: error.message
    });
  }
});

// GET /api/unity/dashboard - Get dashboard data for Unity app
router.get('/dashboard', async (req, res) => {
  try {
    // Get basic counts
    const [productCount, lowStockCount, pendingOrdersCount, todaySalesCount] = await Promise.all([
      Product.countDocuments({ isActive: true }),
      Product.countDocuments({ isActive: true }).then(async () => {
        const products = await Product.find({ isActive: true }).select('currentStock minStockLevel');
        return products.filter(p => p.currentStock <= p.minStockLevel).length;
      }),
      Order.countDocuments({ status: { $in: ['pending', 'confirmed'] } }),
      Sale.countDocuments({ 
        date: { 
          $gte: new Date(new Date().setHours(0, 0, 0, 0)),
          $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
      })
    ]);

    // Get recent activities
    const recentSales = await Sale.find()
      .sort({ date: -1 })
      .limit(5)
      .select('saleNumber totalAmount date customer')
      .lean();

    const urgentOrders = await Order.find({
      deliveryDate: { $lte: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) },
      status: { $nin: ['delivered', 'cancelled'] }
    })
    .sort({ deliveryDate: 1 })
    .limit(5)
    .select('orderNumber customer deliveryDate totalAmount')
    .lean();

    res.json({
      success: true,
      data: {
        summary: {
          totalProducts: productCount,
          lowStockAlerts: lowStockCount,
          pendingOrders: pendingOrdersCount,
          todaySales: todaySalesCount
        },
        recentSales: recentSales.map(sale => ({
          id: sale._id,
          saleNumber: sale.saleNumber,
          amount: sale.totalAmount,
          date: sale.date,
          customer: sale.customer?.name || 'Walk-in'
        })),
        urgentOrders: urgentOrders.map(order => ({
          id: order._id,
          orderNumber: order.orderNumber,
          customer: order.customer.name,
          deliveryDate: order.deliveryDate,
          amount: order.totalAmount
        })),
        alertLevel: lowStockCount > 10 ? 'critical' : 
                   lowStockCount > 5 ? 'warning' : 'normal'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard data',
      error: error.message
    });
  }
});

module.exports = router;
