// @ts-nocheck
import { io } from 'socket.io-client';

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

class SocketService {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
  }

  connect() {
    if (this.socket?.connected) {
      return this.socket;
    }

    this.socket = io(SOCKET_URL, {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Generic event listener
  on(event, callback) {
    if (!this.socket) {
      this.connect();
    }

    this.socket.on(event, callback);

    // Store listener for cleanup
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback);
    }

    // Remove from stored listeners
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Remove all listeners for an event
  removeAllListeners(event) {
    if (this.socket) {
      this.socket.removeAllListeners(event);
    }
    this.listeners.delete(event);
  }

  // Emit event
  emit(event, data) {
    if (this.socket) {
      this.socket.emit(event, data);
    }
  }

  // Product events
  onProductCreated(callback) {
    this.on('product:created', callback);
  }

  onProductUpdated(callback) {
    this.on('product:updated', callback);
  }

  onProductDeleted(callback) {
    this.on('product:deleted', callback);
  }

  onProductStockUpdated(callback) {
    this.on('product:stock_updated', callback);
  }

  // Category events
  onCategoryCreated(callback) {
    this.on('category:created', callback);
  }

  onCategoryUpdated(callback) {
    this.on('category:updated', callback);
  }

  onCategoryDeleted(callback) {
    this.on('category:deleted', callback);
  }

  // Factory events
  onFactoryLogCreated(callback) {
    this.on('factory:log_created', callback);
  }

  onFactoryLogUpdated(callback) {
    this.on('factory:log_updated', callback);
  }

  onFactoryLogDeleted(callback) {
    this.on('factory:log_deleted', callback);
  }

  // Sales events
  onSaleCreated(callback) {
    this.on('sale:created', callback);
  }

  onSaleUpdated(callback) {
    this.on('sale:updated', callback);
  }

  onSaleDeleted(callback) {
    this.on('sale:deleted', callback);
  }

  onSaleRefunded(callback) {
    this.on('sale:refunded', callback);
  }

  // Order events
  onOrderCreated(callback) {
    this.on('order:created', callback);
  }

  onOrderUpdated(callback) {
    this.on('order:updated', callback);
  }

  onOrderDeleted(callback) {
    this.on('order:deleted', callback);
  }

  onOrderStatusUpdated(callback) {
    this.on('order:status_updated', callback);
  }

  // Webhook events
  onWebhookCreated(callback) {
    this.on('webhook:created', callback);
  }

  onWebhookUpdated(callback) {
    this.on('webhook:updated', callback);
  }

  onWebhookDeleted(callback) {
    this.on('webhook:deleted', callback);
  }

  onWebhookToggled(callback) {
    this.on('webhook:toggled', callback);
  }

  // Cleanup all listeners
  cleanup() {
    this.listeners.forEach((callbacks, event) => {
      this.removeAllListeners(event);
    });
    this.listeners.clear();
  }

  // Get connection status
  isConnected() {
    return this.socket?.connected || false;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
