// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton
} from '@mui/material';
import {
  Add as AddIcon,
  PointOfSale as SalesIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { salesAPI, productsAPI } from '../services/api';
import { format } from 'date-fns';

interface Sale {
  _id: string;
  saleNumber: string;
  date: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  items: Array<{
    product: { _id: string; name: string };
    quantity: number;
    unitPrice: number;
    finalPrice: number;
  }>;
  totalAmount: number;
  paymentMethod: string;
  paymentStatus: string;
  salesPerson: string;
}

interface Product {
  _id: string;
  name: string;
  price: number;
  currentStock: number;
}

const SalesTab: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [dashboard, setDashboard] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);

  // Form state for sale creation
  const [formData, setFormData] = useState({
    customer: {
      name: '',
      email: '',
      phone: '',
      address: ''
    },
    items: [{ product: '', quantity: 1, unitPrice: 0 }],
    paymentMethod: 'cash' as 'cash' | 'card' | 'bank_transfer' | 'check' | 'credit',
    salesPerson: '',
    notes: '',
    tax: { rate: 0, amount: 0 },
    discount: 0
  });

  useEffect(() => {
    fetchSales();
    fetchProducts();
    fetchDashboard();
  }, []);

  const fetchSales = async () => {
    try {
      setLoading(true);
      const response = await salesAPI.getAll();
      setSales(response.data.sales);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch sales');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await productsAPI.getAll();
      setProducts(response.data.products);
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
    }
  };

  const fetchDashboard = async () => {
    try {
      const response = await salesAPI.getDashboard();
      setDashboard(response.data);
    } catch (err: any) {
      console.error('Failed to fetch dashboard:', err);
    }
  };

  const handleCreateSale = async () => {
    try {
      await salesAPI.create(formData);
      setOpenDialog(false);
      resetForm();
      fetchSales();
      fetchDashboard();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create sale');
    }
  };

  const resetForm = () => {
    setFormData({
      customer: { name: '', email: '', phone: '', address: '' },
      items: [{ product: '', quantity: 1, unitPrice: 0 }],
      paymentMethod: 'cash',
      salesPerson: '',
      notes: '',
      tax: { rate: 0, amount: 0 },
      discount: 0
    });
  };

  const addItemRow = () => {
    setFormData({
      ...formData,
      items: [...formData.items, { product: '', quantity: 1, unitPrice: 0 }]
    });
  };

  const removeItemRow = (index: number) => {
    const updated = formData.items.filter((_, i) => i !== index);
    setFormData({ ...formData, items: updated });
  };

  const updateItem = (index: number, field: string, value: any) => {
    const updated = [...formData.items];
    updated[index] = { ...updated[index], [field]: value };
    
    // Auto-fill price when product is selected
    if (field === 'product') {
      const selectedProduct = products.find(p => p._id === value);
      if (selectedProduct) {
        updated[index].unitPrice = selectedProduct.price;
      }
    }
    
    setFormData({ ...formData, items: updated });
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'partial': return 'info';
      case 'refunded': return 'error';
      default: return 'default';
    }
  };

  const columns: GridColDef[] = [
    { field: 'saleNumber', headerName: 'Sale Number', width: 150 },
    {
      field: 'date',
      headerName: 'Date',
      width: 120,
      valueFormatter: (params: any) => format(new Date(params.value), 'MMM dd, yyyy')
    },
    {
      field: 'customer',
      headerName: 'Customer',
      width: 200,
      valueGetter: (params: any) => params.row.customer.name || 'Walk-in Customer'
    },
    {
      field: 'totalAmount',
      headerName: 'Total Amount',
      width: 120,
      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`
    },
    { field: 'paymentMethod', headerName: 'Payment Method', width: 130 },
    {
      field: 'paymentStatus',
      headerName: 'Status',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={getPaymentStatusColor(params.value) as any}
          size="small"
        />
      )
    },
    { field: 'salesPerson', headerName: 'Sales Person', width: 150 },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      getActions: (params: any) => [
        <GridActionsCellItem
          icon={<ReceiptIcon />}
          label="View Receipt"
          onClick={() => console.log('View receipt for', params.row._id)}
          key="receipt"
        />
      ]
    }
  ];

  // Chart colors
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const calculateSubtotal = () => {
    return formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const taxAmount = (subtotal * formData.tax.rate) / 100;
    return subtotal + taxAmount - formData.discount;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Sales Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SalesIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{dashboard?.summary?.totalSales || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Sales
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <MoneyIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${dashboard?.summary?.totalRevenue?.toFixed(2) || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Revenue
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${dashboard?.summary?.avgSaleValue?.toFixed(2) || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Average Sale Value
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ReceiptIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${dashboard?.summary?.totalTax?.toFixed(2) || 0}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Tax Collected
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Daily Sales Trend
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={dashboard?.dailyTrend || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="dailyRevenue" stroke="#8884d8" name="Revenue" />
                <Line type="monotone" dataKey="dailySales" stroke="#82ca9d" name="Sales Count" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Payment Methods
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={dashboard?.paymentMethodBreakdown || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ _id, count }) => `${_id}: ${count}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {(dashboard?.paymentMethodBreakdown || []).map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Top Products */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Top Selling Products
        </Typography>
        <Grid container spacing={2}>
          {(dashboard?.topProducts || []).slice(0, 5).map((product: any, index: number) => (
            <Grid item xs={12} sm={6} md={2.4} key={product._id}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>
                    {product.productName}
                  </Typography>
                  <Typography variant="h6" color="primary">
                    ${product.totalRevenue.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {product.totalQuantity} units sold
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          Record New Sale
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Sales Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={sales}
          columns={columns}
          getRowId={(row) => row._id}
          loading={loading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Create Sale Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Record New Sale</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Customer Information</Typography>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.customer.name}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, name: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.customer.email}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, email: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.customer.phone}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  customer: { ...formData.customer, phone: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Sales Person"
                value={formData.salesPerson}
                onChange={(e) => setFormData({ ...formData, salesPerson: e.target.value })}
              />
            </Grid>
          </Grid>

          {/* Sale Items */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Sale Items</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {formData.items.map((item, index) => (
                <Grid container spacing={2} key={index} sx={{ mb: 2 }} alignItems="center">
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Product</InputLabel>
                      <Select
                        value={item.product}
                        label="Product"
                        onChange={(e) => updateItem(index, 'product', e.target.value)}
                      >
                        {products.map((product) => (
                          <MenuItem key={product._id} value={product._id}>
                            {product.name} (Stock: {product.currentStock})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Quantity"
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Unit Price"
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <Typography variant="body2">
                      ${(item.quantity * item.unitPrice).toFixed(2)}
                    </Typography>
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton 
                      size="small" 
                      onClick={() => removeItemRow(index)}
                      disabled={formData.items.length === 1}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              ))}
              <Button onClick={addItemRow} size="small">
                Add Item
              </Button>
            </AccordionDetails>
          </Accordion>

          {/* Payment and Totals */}
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  value={formData.paymentMethod}
                  label="Payment Method"
                  onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value as any })}
                >
                  <MenuItem value="cash">Cash</MenuItem>
                  <MenuItem value="card">Card</MenuItem>
                  <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
                  <MenuItem value="check">Check</MenuItem>
                  <MenuItem value="credit">Credit</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Tax Rate (%)"
                type="number"
                value={formData.tax.rate}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  tax: { ...formData.tax, rate: parseFloat(e.target.value) || 0 }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Discount"
                type="number"
                value={formData.discount}
                onChange={(e) => setFormData({ ...formData, discount: parseFloat(e.target.value) || 0 })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Total Amount"
                value={`$${calculateTotal().toFixed(2)}`}
                InputProps={{ readOnly: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateSale} variant="contained">
            Record Sale
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SalesTab;
